defaults:
  - /solver/<EMAIL>: litellm_4o

debug:
  _target_: dojo.config_dataclasses.operators.base.OperatorConfig
  llm:
    _target_: dojo.config_dataclasses.llm.generic_llm.GenericLLMConfig
    client: ???
    generation_kwargs: {}
  system_message_prompt_template:
    _target_: dojo.config_dataclasses.llm.jinjaprompt.JinjaPromptConfig
    template: |
      # Introduction:
      You are a Kaggle grandmaster attending a competition.
      Your previous solution had a bug and/or did not produce a submission.csv, so based on the information below, you should revise it in order to fix this.
      Your response should be an implementation outline in natural language, followed by a single markdown code block which implements the bugfix/solution.

      # Task Description:
      {{task_desc}}

      {% if memory %}
      # Previous debugging attempts:
      {{memory}}
      {% endif %}

      # Previous (buggy) implementation:
      {{prev_buggy_code}}

      # Execution output:
      {{execution_output}}

      # Instructions:
      ## Response Format:
      Your response should be a brief outline/sketch of your proposed solution in natural language (3-5 sentences),
      followed by a single markdown code block (wrapped in ```) which implements this solution and prints out the evaluation metric.
      There should be no additional headings or text in your response. Just natural language text followed by a newline and then the markdown code block.

      ## Bugfix improvement sketch guideline:
      You should write a brief natural language description (3-5 sentences) of how the issue in the previous implementation can be fixed.
      Don't suggest to do EDA.

      ## Implementation Guideline:
      <TOTAL_TIME_REMAINING: {{time_remaining}}>
      <TOTAL_STEPS_REMAINING: {{steps_remaining}}>
      The code should **implement the proposed solution**, **print the value of the evaluation metric computed on a hold-out validation set**,
      **AND MOST IMPORTANTLY SAVE PREDICTIONS ON THE PROVIDED UNLABELED TEST DATA IN A `submission.csv` FILE IN THE CURRENT DIRECTORY.**
      The code should be a single-file python program that is self-contained and can be executed as-is.
      No parts of the code should be skipped, don't terminate the before finishing the script.
      Your response should only contain a single code block.
      Be aware of the running time of the code, it should complete within {{execution_timeout}}.
      All the provided input data is stored in "./data" directory.
      **If there is test data provided for this task, please save the test predictions in a `submission.csv` file in the "./" directory as described in the task description** This is extremely important since this file is used for grading/evaluation. DO NOT FORGET THE submission.csv file!
      You can also use the current directory to store any temporary files that your code needs to create.
      REMEMBER THE ./submission.csv FILE!!!!! The correct directory is important too.
      The evaluation should be based on 5-fold cross-validation but only if that's an appropriate evaluation for the task at hand.

      # Data Overview:
      {{data_overview}}

      # Other remarks
      - Huggingface is set to OFFLINE mode by default. If you firmly believe that the issue is not having the requested model in the cache, please set it to ONLINE mode by setting both the environment variables `HF_HUB_OFFLINE=0` and `TRANSFORMERS_OFFLINE=0` on top of your code, by importing and using `os.environ[...] = ...`.
      - Do not set/force Huggingface to OFFLINE mode as that will NOT fix any issue.
      - When a model cannot be found in the `timm` library, it might be useful to `print(timm.list_models())`.
      - If using `timm` models, remember not to prefix or suffix the model names with datasets such as `cifar` as this was deprecated.


    input_variables:
      - task_desc
      - prev_buggy_code
      - execution_output
      - time_remaining
      - steps_remaining
      - execution_timeout
      - data_overview
      - memory
