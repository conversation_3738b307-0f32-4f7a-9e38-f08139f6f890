#!/usr/bin/env python3
"""
Test script for meta-learning components in AIRA Dojo.

This script tests the basic functionality of the meta-learning framework
without requiring the full AIRA Dojo environment.
"""

import numpy as np
import torch
from pathlib import Path
import tempfile
import json

# Test imports
try:
    from src.dojo.core.solvers.meta_learning.rl_search_policy import RLSearchPolicy, RLConfig
    from src.dojo.core.solvers.meta_learning.bayesian_optimizer import (
        BayesianOptimizer, BayesianOptimizerConfig, ParameterSpace
    )
    from src.dojo.core.solvers.meta_learning.adaptive_scheduler import AdaptiveScheduler, SchedulerConfig
    from src.dojo.core.solvers.meta_learning.meta_learner import (
        MetaLearner, MetaLearnerConfig, TaskFeatures, TaskExperience
    )
    from src.dojo.core.solvers.meta_learning.nas_integration import (
        NASIntegration, NASConfig, ArchitectureSpace, MockEvaluator
    )
    print("✅ All meta-learning imports successful!")
except ImportError as e:
    print(f"❌ Import error: {e}")
    exit(1)


def test_rl_search_policy():
    """Test RL Search Policy functionality."""
    print("\n🧠 Testing RL Search Policy...")
    
    config = RLConfig(
        state_dim=32,
        action_dim=4,
        hidden_dim=64,
        learning_rate=1e-3,
        epsilon=0.1
    )
    
    policy = RLSearchPolicy(config)
    
    # Test state extraction (mock journal)
    class MockJournal:
        def __init__(self):
            self.nodes = []
            self.good_nodes = []
            self.buggy_nodes = []
            self.draft_nodes = []
    
    journal = MockJournal()
    task_info = {'complexity': 0.5, 'data_size': 1000}
    
    state = policy.extract_state_features(journal, task_info)
    assert state.shape == (32,), f"Expected state shape (32,), got {state.shape}"
    
    # Test action selection
    available_actions = ['draft', 'improve', 'debug']
    action = policy.select_action(state, available_actions)
    assert action in available_actions, f"Invalid action: {action}"
    
    # Test policy update
    reward = 0.5
    next_state = np.random.randn(32)
    policy.update_policy(state, action, reward, next_state, False)
    
    print("✅ RL Search Policy test passed!")


def test_bayesian_optimizer():
    """Test Bayesian Optimizer functionality."""
    print("\n🎯 Testing Bayesian Optimizer...")
    
    # Define parameter spaces
    parameter_spaces = [
        ParameterSpace(
            name='learning_rate',
            param_type='continuous',
            bounds=(1e-5, 1e-1),
            log_scale=True
        ),
        ParameterSpace(
            name='batch_size',
            param_type='discrete',
            choices=[16, 32, 64, 128]
        ),
        ParameterSpace(
            name='optimizer',
            param_type='categorical',
            choices=['adam', 'sgd', 'rmsprop']
        )
    ]
    
    config = BayesianOptimizerConfig(
        n_initial_points=3,
        max_iterations=10
    )
    
    optimizer = BayesianOptimizer(parameter_spaces, config)
    
    # Test parameter suggestion
    params = optimizer.suggest_parameters()
    assert 'learning_rate' in params
    assert 'batch_size' in params
    assert 'optimizer' in params
    
    # Test update
    objective_value = np.random.random()
    optimizer.update(params, objective_value)
    
    # Test multiple iterations
    for i in range(5):
        params = optimizer.suggest_parameters()
        objective = np.random.random()
        optimizer.update(params, objective)
    
    best_params, best_score = optimizer.get_best_parameters()
    assert best_params is not None
    assert isinstance(best_score, float)
    
    print("✅ Bayesian Optimizer test passed!")


def test_adaptive_scheduler():
    """Test Adaptive Scheduler functionality."""
    print("\n🌡️ Testing Adaptive Scheduler...")
    
    config = SchedulerConfig(
        initial_temp=1.0,
        final_temp=0.1,
        adaptation_rate=0.1
    )
    
    # Test different scheduler strategies
    strategies = ['performance', 'diversity', 'linear', 'exponential', 'cosine']
    
    for strategy in strategies:
        scheduler = AdaptiveScheduler(config, strategy, total_steps=100)
        
        # Mock journal for testing
        class MockJournal:
            def __init__(self):
                self.nodes = []
                self.good_nodes = []
            
            def get_best_node(self):
                class MockNode:
                    def __init__(self):
                        class MockMetric:
                            def __init__(self):
                                self.value = np.random.random()
                                self.is_worst = False
                        self.metric = MockMetric()
                return MockNode()
        
        journal = MockJournal()
        
        # Test temperature updates
        initial_temp = scheduler.get_temperature()
        for i in range(10):
            temp = scheduler.update(journal, {})
            assert isinstance(temp, float)
            assert temp > 0
        
        print(f"  ✅ {strategy} scheduler test passed!")
    
    print("✅ Adaptive Scheduler test passed!")


def test_meta_learner():
    """Test Meta-Learner functionality."""
    print("\n🎓 Testing Meta-Learner...")
    
    config = MetaLearnerConfig(
        feature_dim=32,
        hidden_dim=64,
        learning_rate=1e-3
    )
    
    meta_learner = MetaLearner(config)
    
    # Create mock task features
    task_features = TaskFeatures(
        task_type='classification',
        data_size=1000,
        num_features=20,
        target_type='classification',
        complexity_score=0.5,
        domain='tabular',
        evaluation_metric='accuracy',
        time_limit=3600
    )
    
    # Test strategy prediction (should work even without experience)
    strategies = meta_learner.predict_strategy(task_features)
    assert isinstance(strategies, dict)
    assert len(strategies) > 0
    
    # Test hyperparameter prediction
    hyperparams = meta_learner.predict_hyperparameters(task_features)
    assert isinstance(hyperparams, dict)
    assert 'learning_rate' in hyperparams
    
    # Test operator recommendations
    operators = meta_learner.get_operator_recommendations(task_features)
    assert isinstance(operators, dict)
    assert len(operators) > 0
    
    # Create and add task experience
    experience = TaskExperience(
        task_features=task_features,
        solver_config={'learning_rate': 1e-3, 'batch_size': 32},
        performance_history=[0.1, 0.3, 0.5, 0.7, 0.8],
        operator_usage={'draft': 5, 'improve': 10, 'debug': 3, 'analyze': 2},
        operator_success_rates={'draft': 0.8, 'improve': 0.9, 'debug': 0.7, 'analyze': 1.0},
        final_performance=0.8,
        convergence_step=15,
        total_time=1800,
        best_strategies=['high_performance', 'fast_convergence'],
        failure_modes=[]
    )
    
    meta_learner.add_task_experience(experience)
    
    # Test predictions with experience
    strategies_with_exp = meta_learner.predict_strategy(task_features)
    hyperparams_with_exp = meta_learner.predict_hyperparameters(task_features)
    
    print("✅ Meta-Learner test passed!")


def test_nas_integration():
    """Test NAS Integration functionality."""
    print("\n🏗️ Testing NAS Integration...")
    
    config = NASConfig(
        search_strategy='random',
        population_size=5,
        num_generations=3,
        max_evaluations=10
    )
    
    space = ArchitectureSpace(
        max_layers=3,
        layer_types=['linear', 'conv1d'],
        hidden_dims=[32, 64],
        activations=['relu', 'tanh']
    )
    
    evaluator = MockEvaluator()
    
    nas = NASIntegration(config, space, evaluator, input_dim=32, output_dim=10)
    
    # Test architecture search
    best_arch = nas.search()
    assert best_arch is not None
    assert len(best_arch.layers) > 0
    
    # Test architecture properties
    complexity = best_arch.calculate_complexity()
    assert isinstance(complexity, float)
    assert complexity > 0
    
    print("✅ NAS Integration test passed!")


def test_integration():
    """Test integration between components."""
    print("\n🔗 Testing Component Integration...")
    
    # Test that components can work together
    rl_config = RLConfig(state_dim=32, action_dim=4, hidden_dim=32)
    rl_policy = RLSearchPolicy(rl_config)
    
    scheduler_config = SchedulerConfig()
    scheduler = AdaptiveScheduler(scheduler_config, 'performance', total_steps=50)
    
    meta_config = MetaLearnerConfig(feature_dim=32, hidden_dim=32)
    meta_learner = MetaLearner(meta_config)
    
    # Simulate a simple interaction
    class MockJournal:
        def __init__(self):
            self.nodes = []
            self.good_nodes = []
            self.buggy_nodes = []
            self.draft_nodes = []
        
        def get_best_node(self):
            return None
    
    journal = MockJournal()
    task_info = {'complexity': 0.5}
    
    # Test RL policy state extraction
    state = rl_policy.extract_state_features(journal, task_info)
    
    # Test scheduler update
    temp = scheduler.update(journal, task_info)
    
    # Test meta-learner with mock task features
    task_features = TaskFeatures(
        task_type='test',
        data_size=100,
        num_features=10,
        target_type='classification',
        complexity_score=0.5,
        domain='tabular',
        evaluation_metric='accuracy',
        time_limit=3600
    )
    
    strategies = meta_learner.predict_strategy(task_features)
    
    print("✅ Component Integration test passed!")


def main():
    """Run all tests."""
    print("🚀 Starting Meta-Learning Framework Tests...")
    
    try:
        test_rl_search_policy()
        test_bayesian_optimizer()
        test_adaptive_scheduler()
        test_meta_learner()
        test_nas_integration()
        test_integration()
        
        print("\n🎉 All tests passed! Meta-learning framework is working correctly.")
        print("\n📋 Summary:")
        print("  ✅ RL Search Policy: Functional")
        print("  ✅ Bayesian Optimizer: Functional")
        print("  ✅ Adaptive Scheduler: Functional")
        print("  ✅ Meta-Learner: Functional")
        print("  ✅ NAS Integration: Functional")
        print("  ✅ Component Integration: Functional")
        
        print("\n🚀 Ready to use meta-learning capabilities in AIRA Dojo!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        exit(1)


if __name__ == "__main__":
    main()
