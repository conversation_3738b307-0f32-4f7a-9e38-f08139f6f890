# Copyright (c) Meta Platforms, Inc. and affiliates.
# All rights reserved.
#
# This source code is licensed under the license found in the
# LICENSE file in the root directory of this source tree.

"""
Meta-Adaptive Solver with sophisticated meta-learning and adaptive search strategies.

This solver combines multiple advanced techniques:
- RL-based operator selection that learns optimal policies
- Bayesian optimization for automatic hyperparameter tuning
- Meta-learning from previous tasks
- Adaptive temperature scheduling
- Neural architecture search integration
"""

import time
import random
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from functools import partial
from pathlib import Path

import hydra
from dojo.core.solvers.base import Solver
from dojo.core.solvers.operators.analyze import analyze_op
from dojo.core.solvers.operators.core import execute_op_plan_code
from dojo.core.solvers.operators.debug import debug_op
from dojo.core.solvers.operators.draft import draft_op
from dojo.core.solvers.operators.improve import improve_op
from dojo.core.solvers.operators.memory import create_memory_op
from dojo.core.solvers.utils import data_preview
from dojo.core.solvers.utils.journal import Journal, Node
from dojo.core.solvers.utils.metric import MetricValue, WorstMetricValue
from dojo.core.solvers.utils.response import extract_code
from dojo.core.solvers.utils.search_exporter import export_search_results
from dojo.solvers.utils import get_complextiy_level
from dojo.utils.code_parsing import parse_json_output

# Meta-learning imports
from dojo.core.solvers.meta_learning.rl_search_policy import RLSearchPolicy, RLConfig
from dojo.core.solvers.meta_learning.bayesian_optimizer import (
    BayesianOptimizer, BayesianOptimizerConfig, ParameterSpace,
    create_solver_parameter_spaces
)
from dojo.core.solvers.meta_learning.adaptive_scheduler import AdaptiveScheduler, SchedulerConfig
from dojo.core.solvers.meta_learning.meta_learner import (
    MetaLearner, MetaLearnerConfig, TaskFeatures, TaskExperience,
    extract_task_features_from_mlebench
)
from dojo.core.solvers.meta_learning.nas_integration import (
    NASIntegration, NASConfig, ArchitectureSpace, MockEvaluator,
    create_default_architecture_space
)

from dojo.core.tasks.constants import (
    EXECUTION_OUTPUT, VALIDATION_FITNESS, VALID_SOLUTION, TEST_FITNESS, AUX_EVAL_INFO
)
from dojo.utils.logger import get_logger, LogEvent


class MetaAdaptiveSolver(Solver):
    """
    Meta-Adaptive Solver with sophisticated meta-learning and adaptive search strategies.

    This solver integrates multiple advanced AI techniques to create an intelligent
    research agent that learns and adapts across tasks.
    """

    def __init__(self, cfg, task_info=None):
        super().__init__(cfg, task_info)

        # Initialize components
        self._setup_meta_learning_components()
        self._setup_operators()
        self._setup_search_state()

        # Extract task features for meta-learning
        if task_info:
            self.task_features = extract_task_features_from_mlebench(
                task_info.get('name', 'unknown'), task_info
            )
        else:
            self.task_features = None

        # Get meta-learning recommendations
        self._apply_meta_learning_recommendations()

        self.logger.info("MetaAdaptiveSolver initialized with meta-learning capabilities")

    def _setup_meta_learning_components(self):
        """Initialize all meta-learning components."""
        # RL Search Policy
        rl_config = RLConfig(
            state_dim=64,
            action_dim=4,
            hidden_dim=getattr(self.cfg, 'rl_hidden_dim', 128),
            learning_rate=getattr(self.cfg, 'rl_learning_rate', 1e-3),
            epsilon=getattr(self.cfg, 'rl_epsilon', 0.1)
        )

        rl_checkpoint_path = getattr(self.cfg, 'rl_checkpoint_path', None)
        self.rl_policy = RLSearchPolicy(rl_config, rl_checkpoint_path)

        # Bayesian Optimizer for hyperparameters
        bo_config = BayesianOptimizerConfig(
            n_initial_points=getattr(self.cfg, 'bo_initial_points', 5),
            max_iterations=getattr(self.cfg, 'bo_max_iterations', 50),
            acquisition_function=getattr(self.cfg, 'bo_acquisition', 'ei')
        )

        parameter_spaces = create_solver_parameter_spaces()
        bo_checkpoint_path = getattr(self.cfg, 'bo_checkpoint_path', None)
        self.bayesian_optimizer = BayesianOptimizer(
            parameter_spaces, bo_config, bo_checkpoint_path
        )

        # Adaptive Scheduler
        scheduler_config = SchedulerConfig(
            initial_temp=getattr(self.cfg, 'initial_temp', 1.0),
            final_temp=getattr(self.cfg, 'final_temp', 0.1),
            adaptation_rate=getattr(self.cfg, 'adaptation_rate', 0.1)
        )

        scheduler_strategy = getattr(self.cfg, 'scheduler_strategy', 'performance')
        total_steps = getattr(self.cfg, 'step_limit', 500)
        self.adaptive_scheduler = AdaptiveScheduler(
            scheduler_config, scheduler_strategy, total_steps
        )

        # Meta-Learner
        meta_config = MetaLearnerConfig(
            feature_dim=64,
            hidden_dim=getattr(self.cfg, 'meta_hidden_dim', 128),
            learning_rate=getattr(self.cfg, 'meta_learning_rate', 1e-3)
        )

        self.meta_learner = MetaLearner(meta_config)

        # Load existing knowledge base if available
        knowledge_base_path = getattr(self.cfg, 'knowledge_base_path', None)
        if knowledge_base_path and Path(knowledge_base_path + '.json').exists():
            self.meta_learner.load_knowledge_base(knowledge_base_path)

        # NAS Integration (optional)
        if getattr(self.cfg, 'use_nas', False):
            nas_config = NASConfig(
                search_strategy=getattr(self.cfg, 'nas_strategy', 'evolutionary'),
                population_size=getattr(self.cfg, 'nas_population_size', 10),
                num_generations=getattr(self.cfg, 'nas_generations', 20)
            )

            architecture_space = create_default_architecture_space()
            evaluator = MockEvaluator()  # Replace with actual evaluator

            self.nas_integration = NASIntegration(
                nas_config, architecture_space, evaluator,
                input_dim=64, output_dim=32
            )
        else:
            self.nas_integration = None

    def _setup_operators(self):
        """Setup LLM operators with meta-learning enhanced configurations."""
        # Create LLM clients with potentially optimized configurations
        draft_llm = hydra.utils.instantiate(self.cfg.operators.draft.llm)
        improve_llm = hydra.utils.instantiate(self.cfg.operators.improve.llm)
        debug_llm = hydra.utils.instantiate(self.cfg.operators.debug.llm)
        analyze_llm = hydra.utils.instantiate(self.cfg.operators.analyze.llm)

        # Create memory operators
        self.memory_op = create_memory_op(self.cfg.memory)
        self.debug_memory_op = create_memory_op(self.cfg.debug_memory)

        # Create operator functions
        self.draft_fn = partial(draft_op, draft_llm, self.cfg, self.memory_op)
        self.improve_fn = partial(improve_op, improve_llm, self.cfg, self.memory_op)
        self.debug_fn = partial(debug_op, debug_llm, self.cfg, self.debug_memory_op)
        self.analyze_fn = partial(analyze_op, analyze_llm, self.cfg)

        # Operator mapping for RL policy
        self.operator_functions = {
            'draft': self._draft,
            'improve': self._improve,
            'debug': self._debug,
            'analyze': self._analyze
        }

    def _setup_search_state(self):
        """Initialize search state and tracking variables."""
        self.journal = Journal()
        self.data_preview = None
        self.root_node = None

        # Performance tracking
        self.performance_history = []
        self.operator_usage = {'draft': 0, 'improve': 0, 'debug': 0, 'analyze': 0}
        self.operator_success_rates = {'draft': 0.0, 'improve': 0.0, 'debug': 0.0, 'analyze': 0.0}
        self.operator_successes = {'draft': 0, 'improve': 0, 'debug': 0, 'analyze': 0}

        # Meta-learning state
        self.last_rl_state = None
        self.last_rl_action = None
        self.current_temperature = self.adaptive_scheduler.get_temperature()

        # Bayesian optimization state
        self.bo_iteration = 0
        self.current_hyperparams = None

    def _apply_meta_learning_recommendations(self):
        """Apply recommendations from meta-learning components."""
        if not self.task_features:
            return

        # Get strategy recommendations
        strategy_scores = self.meta_learner.predict_strategy(self.task_features)
        self.logger.info(f"Meta-learning strategy recommendations: {strategy_scores}")

        # Get hyperparameter recommendations
        recommended_hyperparams = self.meta_learner.predict_hyperparameters(self.task_features)
        self.logger.info(f"Meta-learning hyperparameter recommendations: {recommended_hyperparams}")

        # Apply hyperparameters to configuration (if not overridden)
        for param, value in recommended_hyperparams.items():
            if not hasattr(self.cfg, param):
                setattr(self.cfg, param, value)

        # Get operator recommendations
        operator_recommendations = self.meta_learner.get_operator_recommendations(self.task_features)
        self.operator_preferences = operator_recommendations
        self.logger.info(f"Meta-learning operator recommendations: {operator_recommendations}")

    def __call__(self, task, state):
        """
        Run the Meta-Adaptive solver with integrated meta-learning capabilities.
        """
        self.logger.info("Starting Meta-Adaptive search with meta-learning")

        # Create root node
        self.create_root_node()

        # Main search loop
        for step in range(self.state.current_step, self.cfg.step_limit):
            start_time = time.monotonic()

            # Update adaptive components
            self._update_adaptive_components()

            # Execute one search step
            state, eval_result = self.step(task, state)

            # Update timing
            step_time = time.monotonic() - start_time
            self.state.running_time += step_time

            self.logger.info(f"Step {self.state.current_step}: Time: {step_time:.3f}s, "
                           f"Total: {self.state.running_time:.3f}s")

            # Update step counter
            self.state.current_step += 1

            # Save checkpoint
            self.save_checkpoint()

            # Check termination conditions
            if self.state.running_time >= self.cfg.time_limit_secs:
                self.logger.info("Time limit reached, stopping search")
                break

            # Check for convergence (optional)
            if self._check_convergence():
                self.logger.info("Convergence detected, stopping search")
                break

        # Finalize and export results
        self._finalize_search()

        # Get best solution
        best_node = self.journal.get_best_node()
        best_code = best_node.code if best_node else None

        return state, best_code

    def _update_adaptive_components(self):
        """Update all adaptive components based on current search state."""
        # Update adaptive temperature scheduler
        self.current_temperature = self.adaptive_scheduler.update(
            self.journal, {'task_features': self.task_features}
        )

        # Update Bayesian optimizer if enabled
        if hasattr(self.cfg, 'use_bayesian_optimization') and self.cfg.use_bayesian_optimization:
            self._update_bayesian_optimization()

    def _update_bayesian_optimization(self):
        """Update Bayesian optimization for hyperparameter tuning."""
        if self.bo_iteration == 0:
            # First iteration - get initial suggestion
            self.current_hyperparams = self.bayesian_optimizer.suggest_parameters()
            self._apply_hyperparameters(self.current_hyperparams)
        elif self.bo_iteration % 10 == 0:  # Update every 10 steps
            # Evaluate current performance and update BO
            best_node = self.journal.get_best_node()
            if best_node and best_node.metric and not best_node.metric.is_worst:
                performance = best_node.metric.value
                self.bayesian_optimizer.update(self.current_hyperparams, performance)

                # Get new suggestion
                self.current_hyperparams = self.bayesian_optimizer.suggest_parameters()
                self._apply_hyperparameters(self.current_hyperparams)

        self.bo_iteration += 1

    def _apply_hyperparameters(self, hyperparams: Dict[str, Any]):
        """Apply hyperparameters to solver configuration."""
        for param, value in hyperparams.items():
            if hasattr(self.cfg, param):
                setattr(self.cfg, param, value)
                self.logger.debug(f"Applied hyperparameter: {param} = {value}")

    def step(self, task, state):
        """Execute a single step of the meta-adaptive search process."""
        self.logger.info(f"Step {self.state.current_step}: Starting meta-adaptive iteration")

        # Update data preview if needed
        if not self.journal.nodes or self.data_preview is None:
            self.update_data_preview(state)

        # Extract current state features for RL policy
        current_state = self.rl_policy.extract_state_features(
            self.journal, {'task_features': self.task_features}
        )

        # Determine available actions based on current state
        available_actions = self._get_available_actions()

        # Select action using RL policy
        selected_action = self.rl_policy.select_action(current_state, available_actions)

        # Execute the selected action
        result_node = self._execute_action(selected_action)

        # Evaluate the result
        state, eval_result = self._evaluate_node(result_node, task, state)

        # Calculate reward for RL policy
        reward = self._calculate_reward(result_node, eval_result)

        # Update RL policy
        if self.last_rl_state is not None:
            self.rl_policy.update_policy(
                self.last_rl_state, self.last_rl_action, reward, current_state, False
            )

        # Update tracking
        self._update_tracking(selected_action, result_node, eval_result)

        # Store current state for next iteration
        self.last_rl_state = current_state
        self.last_rl_action = selected_action

        return state, eval_result

    def _get_available_actions(self) -> List[str]:
        """Determine which actions are available based on current state."""
        available = []

        # Draft is always available
        available.append('draft')

        # Improve is available if we have good nodes
        if self.journal.good_nodes:
            available.append('improve')

        # Debug is available if we have buggy nodes
        if self.journal.buggy_nodes:
            available.append('debug')

        # Analyze is available if we have any nodes
        if self.journal.nodes:
            available.append('analyze')

        return available

    def _execute_action(self, action: str) -> Node:
        """Execute the selected action and return the resulting node."""
        if action == 'draft':
            return self._draft()
        elif action == 'improve':
            return self._improve()
        elif action == 'debug':
            return self._debug()
        elif action == 'analyze':
            return self._analyze()
        else:
            raise ValueError(f"Unknown action: {action}")

    def _draft(self) -> Node:
        """Generate a new solution from scratch."""
        self.logger.info(f"Step {self.state.current_step}: Drafting new solution")

        plan, code, metrics = execute_op_plan_code(
            self.draft_fn,
            self.task_desc,
            self.journal,
            self.state.current_step,
            self.cfg.time_limit_secs - self.state.running_time,
            self.data_preview,
            get_complextiy_level(num=len(self.journal.draft_nodes)) if getattr(self.cfg, 'use_complexity', False) else None,
            self.root_node,
            max_operator_tries=getattr(self.cfg, 'max_llm_call_retries', 3),
        )

        node = Node(
            plan=plan,
            code=code,
            parents=[],
            operators_used=["draft"],
            operators_metrics=[metrics],
        )

        self.journal.append(node)
        self.log_journal()
        return node

    def _improve(self) -> Node:
        """Improve an existing solution."""
        # Select parent node using temperature-based sampling
        parent_node = self._select_parent_for_improvement()

        self.logger.info(f"Step {self.state.current_step}: Improving solution from node {parent_node.id}")

        plan, code, metrics = execute_op_plan_code(
            self.improve_fn,
            self.task_desc,
            self.journal,
            parent_node,
            self.state.current_step,
            self.cfg.time_limit_secs - self.state.running_time,
            self.data_preview,
            get_complextiy_level(num=len(self.journal.improve_nodes)) if getattr(self.cfg, 'use_complexity', False) else None,
            max_operator_tries=getattr(self.cfg, 'max_llm_call_retries', 3),
        )

        node = Node(
            plan=plan,
            code=code,
            parents=[parent_node],
            operators_used=["improve"],
            operators_metrics=[metrics],
        )

        self.journal.append(node)
        self.log_journal()
        return node

    def _debug(self) -> Node:
        """Debug a buggy solution."""
        # Select buggy node to debug
        debuggable_nodes = [
            n for n in self.journal.buggy_nodes
            if n.is_leaf and getattr(n, 'debug_depth', 0) <= getattr(self.cfg, 'max_debug_depth', 5)
        ]

        if not debuggable_nodes:
            # Fallback to draft if no debuggable nodes
            return self._draft()

        parent_node = random.choice(debuggable_nodes)

        self.logger.info(f"Step {self.state.current_step}: Debugging solution from node {parent_node.id}")

        plan, code, metrics = execute_op_plan_code(
            self.debug_fn,
            self.task_desc,
            self.journal,
            parent_node,
            self.state.current_step,
            self.cfg.time_limit_secs - self.state.running_time,
            self.data_preview,
            max_operator_tries=getattr(self.cfg, 'max_llm_call_retries', 3),
        )

        node = Node(
            plan=plan,
            code=code,
            parents=[parent_node],
            operators_used=["debug"],
            operators_metrics=[metrics],
        )

        # Update debug depth
        node.debug_depth = getattr(parent_node, 'debug_depth', 0) + 1

        self.journal.append(node)
        self.log_journal()
        return node

    def _analyze(self) -> Node:
        """Analyze an existing solution."""
        # Select node to analyze
        if self.journal.good_nodes:
            parent_node = random.choice(self.journal.good_nodes)
        elif self.journal.nodes:
            parent_node = random.choice(self.journal.nodes)
        else:
            # Fallback to draft if no nodes
            return self._draft()

        self.logger.info(f"Step {self.state.current_step}: Analyzing solution from node {parent_node.id}")

        # Analyze the node
        analysis, metrics = self.analyze_fn(self.task_desc, parent_node)

        # Create analysis node (doesn't generate new code)
        node = Node(
            plan=f"Analysis of node {parent_node.id}",
            code=parent_node.code,  # Keep same code
            parents=[parent_node],
            operators_used=["analyze"],
            operators_metrics=[metrics],
            analysis=analysis
        )

        self.journal.append(node)
        self.log_journal()
        return node

    def _select_parent_for_improvement(self) -> Node:
        """Select parent node for improvement using temperature-based sampling."""
        good_nodes = self.journal.good_nodes
        if not good_nodes:
            # Fallback to any node
            good_nodes = [n for n in self.journal.nodes if not n.is_buggy]
            if not good_nodes:
                good_nodes = self.journal.nodes

        if len(good_nodes) == 1:
            return good_nodes[0]

        # Temperature-based selection
        scores = []
        for node in good_nodes:
            if node.metric and not node.metric.is_worst:
                scores.append(node.metric.value)
            else:
                scores.append(0.0)

        # Apply temperature scaling
        if self.current_temperature > 0:
            scores = np.array(scores) / self.current_temperature
            probs = np.exp(scores - np.max(scores))  # Numerical stability
            probs = probs / np.sum(probs)

            # Sample based on probabilities
            selected_idx = np.random.choice(len(good_nodes), p=probs)
            return good_nodes[selected_idx]
        else:
            # Greedy selection
            best_idx = np.argmax(scores)
            return good_nodes[best_idx]

    def _evaluate_node(self, node: Node, task, state) -> Tuple[Any, Dict[str, Any]]:
        """Evaluate a node using the task."""
        try:
            # Execute the solution
            state, eval_result = task.step_task(state, node.code)

            # Process evaluation result
            self._process_evaluation_result(node, eval_result)

            return state, eval_result

        except Exception as e:
            self.logger.error(f"Error evaluating node: {e}")
            # Mark as buggy
            node.is_buggy = True
            node.metric = WorstMetricValue()
            eval_result = {
                EXECUTION_OUTPUT: None,
                VALIDATION_FITNESS: None,
                VALID_SOLUTION: False
            }
            return state, eval_result

    def _process_evaluation_result(self, node: Node, eval_result: Dict[str, Any]):
        """Process evaluation result and update node."""
        # Extract execution output
        exec_output = eval_result.get(EXECUTION_OUTPUT)
        if exec_output:
            node.absorb_exec_result(exec_output)

        # Check if solution is valid
        is_valid = eval_result.get(VALID_SOLUTION, False)

        if not is_valid or node.is_buggy:
            node.metric = WorstMetricValue()
            return

        # Extract fitness value
        fitness = eval_result.get(VALIDATION_FITNESS)
        if fitness is not None:
            # Determine if we're maximizing or minimizing
            maximize = not getattr(self.cfg, 'lower_is_better', False)
            node.metric = MetricValue(fitness, maximize=maximize)
        else:
            node.metric = WorstMetricValue()

        # Store auxiliary evaluation info
        aux_info = eval_result.get(AUX_EVAL_INFO, {})
        if node.metric and hasattr(node.metric, 'info'):
            node.metric.info.update(aux_info)

    def _calculate_reward(self, node: Node, eval_result: Dict[str, Any]) -> float:
        """Calculate reward for RL policy based on node performance."""
        if node.is_buggy or not node.metric or node.metric.is_worst:
            return -1.0  # Penalty for buggy solutions

        # Base reward from performance
        performance = node.metric.value

        # Normalize performance (assuming 0-1 range, adjust as needed)
        reward = performance

        # Bonus for improvement over previous best
        best_node = self.journal.get_best_node()
        if best_node and best_node != node and best_node.metric and not best_node.metric.is_worst:
            if performance > best_node.metric.value:
                improvement = performance - best_node.metric.value
                reward += improvement * 2.0  # Bonus for improvement

        # Penalty for execution time (encourage efficiency)
        if hasattr(node, 'exec_time') and node.exec_time:
            time_penalty = min(node.exec_time / 60.0, 1.0)  # Normalize to minutes
            reward -= time_penalty * 0.1

        return reward

    def _update_tracking(self, action: str, node: Node, eval_result: Dict[str, Any]):
        """Update tracking statistics."""
        # Update operator usage
        self.operator_usage[action] += 1

        # Update success rates
        is_success = not node.is_buggy and node.metric and not node.metric.is_worst
        if is_success:
            self.operator_successes[action] += 1

        # Calculate success rate
        if self.operator_usage[action] > 0:
            self.operator_success_rates[action] = (
                self.operator_successes[action] / self.operator_usage[action]
            )

        # Update performance history
        if node.metric and not node.metric.is_worst:
            self.performance_history.append(node.metric.value)

        # Log statistics
        self.logger.debug(f"Action: {action}, Success: {is_success}, "
                         f"Usage: {self.operator_usage}, "
                         f"Success rates: {self.operator_success_rates}")

    def _check_convergence(self) -> bool:
        """Check if search has converged."""
        if len(self.performance_history) < 20:
            return False

        # Check if performance has plateaued
        recent_performance = self.performance_history[-10:]
        older_performance = self.performance_history[-20:-10]

        recent_mean = np.mean(recent_performance)
        older_mean = np.mean(older_performance)

        # Convergence if improvement is very small
        improvement = recent_mean - older_mean
        threshold = getattr(self.cfg, 'convergence_threshold', 0.001)

        return improvement < threshold

    def _finalize_search(self):
        """Finalize search and save meta-learning experience."""
        # Export search results
        if getattr(self.cfg, 'export_search_results', True):
            export_search_results(self.cfg, self.journal, self.logger, "meta_adaptive")

        # Save RL policy checkpoint
        if hasattr(self.cfg, 'rl_checkpoint_path') and self.cfg.rl_checkpoint_path:
            self.rl_policy.save_checkpoint(self.cfg.rl_checkpoint_path)

        # Save Bayesian optimizer checkpoint
        if hasattr(self.cfg, 'bo_checkpoint_path') and self.cfg.bo_checkpoint_path:
            self.bayesian_optimizer.save_checkpoint(self.cfg.bo_checkpoint_path)

        # Create task experience for meta-learning
        if self.task_features:
            self._create_task_experience()

        # Save knowledge base
        if hasattr(self.cfg, 'knowledge_base_path') and self.cfg.knowledge_base_path:
            self.meta_learner.save_knowledge_base(self.cfg.knowledge_base_path)

    def _create_task_experience(self):
        """Create task experience for meta-learning."""
        # Get final performance
        best_node = self.journal.get_best_node()
        final_performance = (
            best_node.metric.value if best_node and best_node.metric and not best_node.metric.is_worst
            else 0.0
        )

        # Find convergence step
        convergence_step = len(self.performance_history)
        if len(self.performance_history) > 10:
            # Find when performance stopped improving significantly
            for i in range(10, len(self.performance_history)):
                recent_window = self.performance_history[i-10:i]
                if np.std(recent_window) < 0.01:  # Low variance indicates convergence
                    convergence_step = i
                    break

        # Determine best strategies based on performance
        best_strategies = []
        if final_performance > 0.8:
            best_strategies.append('high_performance')
        if convergence_step < len(self.performance_history) * 0.5:
            best_strategies.append('fast_convergence')
        if len(self.journal.good_nodes) > len(self.journal.nodes) * 0.7:
            best_strategies.append('robust')

        # Create experience
        experience = TaskExperience(
            task_features=self.task_features,
            solver_config=self.cfg.__dict__ if hasattr(self.cfg, '__dict__') else {},
            performance_history=self.performance_history,
            operator_usage=self.operator_usage,
            operator_success_rates=self.operator_success_rates,
            final_performance=final_performance,
            convergence_step=convergence_step,
            total_time=self.state.running_time,
            best_strategies=best_strategies,
            failure_modes=[]  # Could be enhanced to detect failure patterns
        )

        # Add to meta-learner
        self.meta_learner.add_task_experience(experience)

        self.logger.info(f"Created task experience: performance={final_performance:.4f}, "
                        f"convergence_step={convergence_step}, strategies={best_strategies}")

    def create_root_node(self):
        """Create root node for the search."""
        self.root_node = Node(
            code="",
            plan="Root node",
            analysis="",
            metric=WorstMetricValue(),
            is_buggy=True,
        )
        self.root_node.absorb_exec_result(None)
        self.journal.append(self.root_node)
        self.log_journal()
        self.state.current_step += 1

    def update_data_preview(self, state):
        """Update data preview for operators."""
        if hasattr(self.cfg, 'data_preview') and self.cfg.data_preview:
            try:
                self.data_preview = data_preview.get_data_preview(state)
            except Exception as e:
                self.logger.warning(f"Failed to get data preview: {e}")
                self.data_preview = None

    def log_journal(self):
        """Log journal state."""
        best_node = self.journal.get_best_node()
        best_node_step = 0 if best_node is None else getattr(best_node, 'step', 0)

        log_data = self.journal.get_node_data(self.state.current_step)
        log_data.update({
            "current_best_node": best_node_step,
            "temperature": self.current_temperature,
            "operator_usage": self.operator_usage,
            "operator_success_rates": self.operator_success_rates
        })

        self.logger.log(log_data, "JOURNAL", step=self.state.current_step)