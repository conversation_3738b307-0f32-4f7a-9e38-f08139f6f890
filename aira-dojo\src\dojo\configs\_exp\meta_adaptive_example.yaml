# @package _global_

# Example experiment configuration for Meta-Adaptive Solver
# This demonstrates the new meta-learning and adaptive search capabilities

defaults:
  - default_run
  - override task: mlebench/tabular_playground_series_jan_2021
  - override solver: mlebench/meta_adaptive
  - override interpreter: jupyter
  - override launcher: local
  - override logger: base_logger

# Experiment metadata
metadata:
  experiment_name: "meta_adaptive_example"
  description: "Example run using Meta-Adaptive Solver with meta-learning capabilities"
  tags: ["meta-learning", "adaptive", "rl", "bayesian-optimization"]

# Solver configuration overrides
solver:
  # Enable all meta-learning components
  use_rl_policy: true
  use_bayesian_optimization: true
  use_adaptive_scheduling: true
  use_meta_learning: true
  use_nas: false  # Disabled for this example
  
  # Search parameters
  step_limit: 100  # Reduced for example
  max_debug_depth: 5
  data_preview: true
  
  # RL Policy settings
  rl_hidden_dim: 64  # Smaller for faster training
  rl_learning_rate: 1e-3
  rl_epsilon: 0.15  # Higher exploration for example
  
  # Bayesian Optimization settings
  bo_initial_points: 3  # Fewer initial points for example
  bo_max_iterations: 20
  bo_acquisition: "ei"
  
  # Adaptive Scheduling settings
  initial_temp: 1.5  # Higher initial exploration
  final_temp: 0.05
  adaptation_rate: 0.15
  scheduler_strategy: "performance"
  
  # Meta-Learning settings
  meta_hidden_dim: 64
  meta_learning_rate: 1e-3
  
  # Advanced features
  enable_operator_adaptation: true
  enable_dynamic_hyperparams: true
  enable_cross_task_learning: true
  
  # Performance monitoring
  performance_window_size: 5
  early_stopping_patience: 15
  min_improvement_threshold: 0.005
  convergence_threshold: 0.001
  
  # Logging
  detailed_logging: true
  export_search_results: true

# Logger configuration
logger:
  use_wandb: false  # Disabled for example
  use_console: true
  use_json: true
  detailed_logging: true
  tags: ["meta-adaptive", "example", "meta-learning"]

# Task configuration
task:
  name: tabular_playground_series_jan_2021

# Interpreter configuration  
interpreter:
  timeout: 300  # 5 minutes timeout for example
