# Copyright (c) Meta Platforms, Inc. and affiliates.
# All rights reserved.
#
# This source code is licensed under the license found in the
# LICENSE file in the root directory of this source tree.

"""
Neural Architecture Search (NAS) Integration for automated model architecture discovery.

This module provides NAS capabilities for automatically discovering optimal
neural network architectures for different tasks and domains.
"""

import numpy as np
import torch
import torch.nn as nn
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, field
import json
from pathlib import Path
from abc import ABC, abstractmethod
import random

from dojo.utils.logger import get_logger


@dataclass
class ArchitectureSpace:
    """Defines the search space for neural architectures."""
    max_layers: int = 6
    layer_types: List[str] = field(default_factory=lambda: ['linear', 'conv1d', 'lstm', 'attention'])
    hidden_dims: List[int] = field(default_factory=lambda: [64, 128, 256, 512])
    activations: List[str] = field(default_factory=lambda: ['relu', 'tanh', 'gelu', 'swish'])
    dropout_rates: List[float] = field(default_factory=lambda: [0.0, 0.1, 0.2, 0.3])
    attention_heads: List[int] = field(default_factory=lambda: [1, 2, 4, 8])
    kernel_sizes: List[int] = field(default_factory=lambda: [3, 5, 7])
    pooling_types: List[str] = field(default_factory=lambda: ['max', 'avg', 'adaptive'])


@dataclass
class NASConfig:
    """Configuration for Neural Architecture Search."""
    search_strategy: str = 'evolutionary'  # 'random', 'evolutionary', 'differentiable'
    population_size: int = 20
    num_generations: int = 50
    mutation_rate: float = 0.1
    crossover_rate: float = 0.7
    tournament_size: int = 3
    max_evaluations: int = 1000
    early_stopping_patience: int = 10
    performance_threshold: float = 0.95
    architecture_complexity_weight: float = 0.1


class Architecture:
    """Represents a neural network architecture."""
    
    def __init__(self, layers: List[Dict[str, Any]], input_dim: int, output_dim: int):
        self.layers = layers
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.performance = None
        self.complexity = None
        self.id = self._generate_id()
    
    def _generate_id(self) -> str:
        """Generate unique ID for architecture."""
        layer_str = "_".join([f"{l['type']}{l.get('hidden_dim', '')}" for l in self.layers])
        return f"arch_{hash(layer_str) % 10000:04d}"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert architecture to dictionary."""
        return {
            'id': self.id,
            'layers': self.layers,
            'input_dim': self.input_dim,
            'output_dim': self.output_dim,
            'performance': self.performance,
            'complexity': self.complexity
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Architecture':
        """Create architecture from dictionary."""
        arch = cls(data['layers'], data['input_dim'], data['output_dim'])
        arch.id = data['id']
        arch.performance = data.get('performance')
        arch.complexity = data.get('complexity')
        return arch
    
    def mutate(self, space: ArchitectureSpace, mutation_rate: float) -> 'Architecture':
        """Create a mutated version of this architecture."""
        new_layers = []
        
        for layer in self.layers:
            if random.random() < mutation_rate:
                # Mutate this layer
                new_layer = layer.copy()
                
                # Randomly change one property
                mutation_type = random.choice(['type', 'hidden_dim', 'activation', 'dropout'])
                
                if mutation_type == 'type' and 'type' in new_layer:
                    new_layer['type'] = random.choice(space.layer_types)
                elif mutation_type == 'hidden_dim' and 'hidden_dim' in new_layer:
                    new_layer['hidden_dim'] = random.choice(space.hidden_dims)
                elif mutation_type == 'activation' and 'activation' in new_layer:
                    new_layer['activation'] = random.choice(space.activations)
                elif mutation_type == 'dropout' and 'dropout' in new_layer:
                    new_layer['dropout'] = random.choice(space.dropout_rates)
                
                new_layers.append(new_layer)
            else:
                new_layers.append(layer.copy())
        
        # Possibly add or remove a layer
        if random.random() < mutation_rate:
            if len(new_layers) < space.max_layers and random.random() < 0.5:
                # Add layer
                new_layer = self._generate_random_layer(space)
                insert_pos = random.randint(0, len(new_layers))
                new_layers.insert(insert_pos, new_layer)
            elif len(new_layers) > 1 and random.random() < 0.5:
                # Remove layer
                remove_pos = random.randint(0, len(new_layers) - 1)
                new_layers.pop(remove_pos)
        
        return Architecture(new_layers, self.input_dim, self.output_dim)
    
    def crossover(self, other: 'Architecture', space: ArchitectureSpace) -> 'Architecture':
        """Create offspring through crossover with another architecture."""
        # Simple crossover: take layers from both parents
        min_layers = min(len(self.layers), len(other.layers))
        crossover_point = random.randint(1, min_layers)
        
        new_layers = self.layers[:crossover_point] + other.layers[crossover_point:]
        
        # Ensure we don't exceed max layers
        if len(new_layers) > space.max_layers:
            new_layers = new_layers[:space.max_layers]
        
        return Architecture(new_layers, self.input_dim, self.output_dim)
    
    def _generate_random_layer(self, space: ArchitectureSpace) -> Dict[str, Any]:
        """Generate a random layer configuration."""
        layer_type = random.choice(space.layer_types)
        layer = {'type': layer_type}
        
        if layer_type in ['linear', 'conv1d']:
            layer['hidden_dim'] = random.choice(space.hidden_dims)
            layer['activation'] = random.choice(space.activations)
            layer['dropout'] = random.choice(space.dropout_rates)
        
        if layer_type == 'conv1d':
            layer['kernel_size'] = random.choice(space.kernel_sizes)
        
        if layer_type == 'attention':
            layer['hidden_dim'] = random.choice(space.hidden_dims)
            layer['num_heads'] = random.choice(space.attention_heads)
            layer['dropout'] = random.choice(space.dropout_rates)
        
        if layer_type == 'lstm':
            layer['hidden_dim'] = random.choice(space.hidden_dims)
            layer['dropout'] = random.choice(space.dropout_rates)
        
        return layer
    
    def calculate_complexity(self) -> float:
        """Calculate architecture complexity score."""
        complexity = 0
        
        for layer in self.layers:
            # Base complexity for layer type
            if layer['type'] == 'linear':
                complexity += 1
            elif layer['type'] == 'conv1d':
                complexity += 2
            elif layer['type'] == 'lstm':
                complexity += 3
            elif layer['type'] == 'attention':
                complexity += 4
            
            # Add complexity for hidden dimensions
            hidden_dim = layer.get('hidden_dim', 64)
            complexity += hidden_dim / 512  # Normalize
            
            # Add complexity for attention heads
            if 'num_heads' in layer:
                complexity += layer['num_heads'] / 8
        
        self.complexity = complexity
        return complexity


class ArchitectureEvaluator(ABC):
    """Abstract base class for evaluating architectures."""
    
    @abstractmethod
    def evaluate(self, architecture: Architecture) -> float:
        """Evaluate architecture and return performance score."""
        pass


class MockEvaluator(ArchitectureEvaluator):
    """Mock evaluator for testing purposes."""
    
    def evaluate(self, architecture: Architecture) -> float:
        """Mock evaluation based on architecture properties."""
        # Simple heuristic: prefer moderate complexity
        complexity = architecture.calculate_complexity()
        
        # Optimal complexity around 5-10
        complexity_score = 1.0 - abs(complexity - 7.5) / 10.0
        
        # Add some randomness
        noise = random.gauss(0, 0.1)
        
        score = max(0.0, min(1.0, complexity_score + noise))
        architecture.performance = score
        return score


class NASIntegration:
    """Neural Architecture Search integration for AIRA Dojo."""
    
    def __init__(self, config: NASConfig, space: ArchitectureSpace,
                 evaluator: ArchitectureEvaluator, input_dim: int, output_dim: int):
        self.config = config
        self.space = space
        self.evaluator = evaluator
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.logger = get_logger()
        
        # Search state
        self.population = []
        self.generation = 0
        self.evaluations = 0
        self.best_architecture = None
        self.best_score = float('-inf')
        self.history = []
        
        # Early stopping
        self.no_improvement_count = 0
        self.last_best_score = float('-inf')
    
    def search(self) -> Architecture:
        """Run neural architecture search."""
        self.logger.info(f"Starting NAS with strategy: {self.config.search_strategy}")
        
        if self.config.search_strategy == 'random':
            return self._random_search()
        elif self.config.search_strategy == 'evolutionary':
            return self._evolutionary_search()
        else:
            raise ValueError(f"Unknown search strategy: {self.config.search_strategy}")
    
    def _random_search(self) -> Architecture:
        """Random architecture search."""
        best_arch = None
        best_score = float('-inf')
        
        for i in range(self.config.max_evaluations):
            arch = self._generate_random_architecture()
            score = self.evaluator.evaluate(arch)
            self.evaluations += 1
            
            if score > best_score:
                best_score = score
                best_arch = arch
                self.logger.info(f"New best architecture found: {score:.4f}")
            
            self.history.append({
                'evaluation': i,
                'architecture': arch.to_dict(),
                'score': score
            })
            
            # Early stopping
            if score >= self.config.performance_threshold:
                self.logger.info(f"Performance threshold reached: {score:.4f}")
                break
        
        self.best_architecture = best_arch
        self.best_score = best_score
        return best_arch
    
    def _evolutionary_search(self) -> Architecture:
        """Evolutionary architecture search."""
        # Initialize population
        self._initialize_population()
        
        for gen in range(self.config.num_generations):
            self.generation = gen
            
            # Evaluate population
            self._evaluate_population()
            
            # Check for improvement
            current_best = max(self.population, key=lambda x: x.performance)
            if current_best.performance > self.best_score:
                self.best_score = current_best.performance
                self.best_architecture = current_best
                self.no_improvement_count = 0
                self.logger.info(f"Gen {gen}: New best architecture: {self.best_score:.4f}")
            else:
                self.no_improvement_count += 1
            
            # Early stopping
            if (self.no_improvement_count >= self.config.early_stopping_patience or
                self.best_score >= self.config.performance_threshold):
                self.logger.info(f"Early stopping at generation {gen}")
                break
            
            # Create next generation
            self._create_next_generation()
            
            # Log generation statistics
            scores = [arch.performance for arch in self.population]
            self.logger.debug(f"Gen {gen}: avg={np.mean(scores):.4f}, "
                            f"best={np.max(scores):.4f}, std={np.std(scores):.4f}")
        
        return self.best_architecture
    
    def _initialize_population(self):
        """Initialize random population."""
        self.population = []
        for _ in range(self.config.population_size):
            arch = self._generate_random_architecture()
            self.population.append(arch)
    
    def _generate_random_architecture(self) -> Architecture:
        """Generate a random architecture."""
        num_layers = random.randint(1, self.space.max_layers)
        layers = []
        
        for _ in range(num_layers):
            layer_type = random.choice(self.space.layer_types)
            layer = {'type': layer_type}
            
            if layer_type in ['linear', 'conv1d']:
                layer['hidden_dim'] = random.choice(self.space.hidden_dims)
                layer['activation'] = random.choice(self.space.activations)
                layer['dropout'] = random.choice(self.space.dropout_rates)
            
            if layer_type == 'conv1d':
                layer['kernel_size'] = random.choice(self.space.kernel_sizes)
            
            if layer_type == 'attention':
                layer['hidden_dim'] = random.choice(self.space.hidden_dims)
                layer['num_heads'] = random.choice(self.space.attention_heads)
                layer['dropout'] = random.choice(self.space.dropout_rates)
            
            if layer_type == 'lstm':
                layer['hidden_dim'] = random.choice(self.space.hidden_dims)
                layer['dropout'] = random.choice(self.space.dropout_rates)
            
            layers.append(layer)
        
        return Architecture(layers, self.input_dim, self.output_dim)
    
    def _evaluate_population(self):
        """Evaluate all architectures in population."""
        for arch in self.population:
            if arch.performance is None:
                score = self.evaluator.evaluate(arch)
                self.evaluations += 1
                
                self.history.append({
                    'generation': self.generation,
                    'evaluation': self.evaluations,
                    'architecture': arch.to_dict(),
                    'score': score
                })
    
    def _create_next_generation(self):
        """Create next generation through selection, crossover, and mutation."""
        new_population = []
        
        # Elitism: keep best architectures
        elite_size = max(1, self.config.population_size // 10)
        elite = sorted(self.population, key=lambda x: x.performance, reverse=True)[:elite_size]
        new_population.extend(elite)
        
        # Generate offspring
        while len(new_population) < self.config.population_size:
            if random.random() < self.config.crossover_rate:
                # Crossover
                parent1 = self._tournament_selection()
                parent2 = self._tournament_selection()
                offspring = parent1.crossover(parent2, self.space)
            else:
                # Mutation only
                parent = self._tournament_selection()
                offspring = parent.mutate(self.space, self.config.mutation_rate)
            
            new_population.append(offspring)
        
        self.population = new_population[:self.config.population_size]
    
    def _tournament_selection(self) -> Architecture:
        """Tournament selection for parent selection."""
        tournament = random.sample(self.population, self.config.tournament_size)
        return max(tournament, key=lambda x: x.performance)
    
    def get_best_architecture(self) -> Tuple[Architecture, float]:
        """Get the best architecture found."""
        return self.best_architecture, self.best_score
    
    def get_search_history(self) -> List[Dict[str, Any]]:
        """Get the search history."""
        return self.history
    
    def save_results(self, path: str):
        """Save search results."""
        results = {
            'config': self.config.__dict__,
            'space': self.space.__dict__,
            'best_architecture': self.best_architecture.to_dict() if self.best_architecture else None,
            'best_score': self.best_score,
            'history': self.history,
            'evaluations': self.evaluations
        }
        
        with open(path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        self.logger.info(f"NAS results saved to {path}")


def create_default_architecture_space() -> ArchitectureSpace:
    """Create default architecture search space."""
    return ArchitectureSpace(
        max_layers=6,
        layer_types=['linear', 'conv1d', 'lstm', 'attention'],
        hidden_dims=[64, 128, 256, 512],
        activations=['relu', 'tanh', 'gelu', 'swish'],
        dropout_rates=[0.0, 0.1, 0.2, 0.3],
        attention_heads=[1, 2, 4, 8],
        kernel_sizes=[3, 5, 7],
        pooling_types=['max', 'avg', 'adaptive']
    )
