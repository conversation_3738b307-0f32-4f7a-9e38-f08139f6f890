defaults:
  - memory: simple_memory  # Enables Memory operator
  - memory@debug_memory: debug_memory # Enables Debug Memory operator
  
# Specifies the Meta-Adaptive solver with meta-learning capabilities
_target_: dojo.config_dataclasses.solver.meta_adaptive.MetaAdaptiveSolverConfig

# Whether to export search results after execution
export_search_results: true

# operators
operators: ???

# --- Basic Search Configuration ---
step_limit: ??? # Maximum number of nodes the search algorithm can explore
max_llm_call_retries: 3 # Maximum retry attempts for failed LLM API calls
max_debug_depth: ??? # Maximum depth of debugging analysis

# --- Agent Configuration ---
data_preview: ??? # Whether to provide the agent with a preview of the data before execution

# --- Meta-Learning Components ---
use_rl_policy: true # Enable RL-based operator selection
use_bayesian_optimization: true # Enable Bayesian optimization for hyperparameters
use_adaptive_scheduling: true # Enable adaptive temperature scheduling
use_meta_learning: true # Enable meta-learning from previous tasks
use_nas: false # Enable Neural Architecture Search (optional)

# --- RL Policy Configuration ---
rl_hidden_dim: 128 # Hidden dimension for RL policy network
rl_learning_rate: 1e-3 # Learning rate for RL policy
rl_epsilon: 0.1 # Epsilon for epsilon-greedy exploration
rl_checkpoint_path: "" # Path to save/load RL policy checkpoint

# --- Bayesian Optimization Configuration ---
bo_initial_points: 5 # Number of initial random points for BO
bo_max_iterations: 50 # Maximum iterations for BO
bo_acquisition: "ei" # Acquisition function (ei, pi, ucb)
bo_checkpoint_path: "" # Path to save/load BO checkpoint

# --- Adaptive Scheduling Configuration ---
initial_temp: 1.0 # Initial temperature for exploration
final_temp: 0.1 # Final temperature for exploitation
adaptation_rate: 0.1 # Rate of adaptation
scheduler_strategy: "performance" # Strategy (performance, diversity, multi_objective)

# --- Meta-Learning Configuration ---
meta_hidden_dim: 128 # Hidden dimension for meta-learning networks
meta_learning_rate: 1e-3 # Learning rate for meta-learning
knowledge_base_path: "" # Path to save/load knowledge base

# --- NAS Configuration (if enabled) ---
nas_strategy: "evolutionary" # Strategy (random, evolutionary, differentiable)
nas_population_size: 10 # Population size for evolutionary NAS
nas_generations: 20 # Number of generations for evolutionary NAS

# --- Search Behavior Configuration ---
max_debug_depth: 5 # Maximum depth for debugging cycles
convergence_threshold: 0.001 # Threshold for convergence detection
use_complexity: false # Whether to use complexity-aware prompting
lower_is_better: false # Whether lower metric values are better

# --- Advanced Features ---
enable_operator_adaptation: true # Adapt operator selection based on performance
enable_dynamic_hyperparams: true # Dynamically adjust hyperparameters
enable_cross_task_learning: true # Learn from previous tasks

# --- Performance Monitoring ---
performance_window_size: 10 # Window size for performance monitoring
early_stopping_patience: 20 # Patience for early stopping
min_improvement_threshold: 0.01 # Minimum improvement threshold

# --- Logging Configuration ---
detailed_logging: true # Enable detailed logging of meta-learning components
