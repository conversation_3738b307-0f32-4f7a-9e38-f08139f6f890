# @package _global_
defaults:
  - override /interpreter: jupyter
  - override /solver: mlebench/greedy
  - override /task: mlebench/aerial-cactus-identification
  - override /solver/<EMAIL>: litellm_4o
  - override /solver/<EMAIL>: litellm_4o
  - override /solver/<EMAIL>: litellm_4o
  - override /solver/<EMAIL>: litellm_4o

metadata:
  git_issue_id: example # Ideally, this should be a number fetched from github issue when running an actual experiment.

solver:
  step_limit: 5