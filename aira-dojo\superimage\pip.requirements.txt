absl_py==2.1.0
accelerate
anthropic>=0.20.0
arrow
backoff
bayesian-optimization
bayesian-optimization
bayesian-optimization
bayespy==0.5.1
biopython
# AIDE requirements
black==24.3.0
catboost
contractions
coolname>=2.2.0
# General requirements
dataclasses-json==0.6.7
dataclasses-json>=0.6.4
datasets==2.1.0
demoji
fastai
fasttext
funcy==2.0
gensim
genson>=1.2.0
git+https://github.com/daviddavo/lightfm
gym
h5py
hmmlearn
humanize==4.11.0
hydra-colorlog==1.2.0
hydra-core==1.3.2
hyperopt
igraph>=0.11.3
imagecodecs
imbalanced-learn
imgaug
implicit
ipykernel
ipython==8.31
jsonlines
jsonschema==4.19.2
kornia
librosa
lightgbm

# Additional packages
lightgbm
lime
litellm
loguru==0.7.2
markovify
matplotlib
mlxtend
nbformat
nltk
node2vec
numba
numpy
numpy==1.26.2
omegaconf>=2.3.0
onnxruntime
openai>=1.3.5
opencv-python
optuna
pandas
pandas==2.1.4
pdf2image
peft
Pillow
plotly
pyarrow
pydicom
pyocr
pyparsing
PyPDF
pytest==7.4.3
python-dotenv
pytorch-lightning
pytz
PyYAML
rank_bm25
requests==2.32.4
rich==13.7.0
scikeras
scikit-image
scikit-learn
scikit-learn
scikit-optimize
scipy==1.11.4
seaborn
sentence_transformers
sentencepiece
shap
shutup==0.2.0
sklearn-pandas
spacey
statsmodels
streamlit==1.40.2
sympy
tenacity==9.0.0
tensorpack
textblob
textstat
timm
timm
torch
torch-geometric
torchinfo
torchmetrics
torchvision
tqdm==4.66.3
transformers
umap-learn
vaderSentiment
wandb
weave
xgboost
xlrd
