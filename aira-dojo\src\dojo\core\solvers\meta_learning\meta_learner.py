# Copyright (c) Meta Platforms, Inc. and affiliates.
# All rights reserved.
#
# This source code is licensed under the license found in the
# LICENSE file in the root directory of this source tree.

"""
Meta-Learning framework for learning from previous tasks to improve on new ones.

This module implements meta-learning capabilities that allow solvers to
leverage experience from previous tasks to make better decisions on new tasks.
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass, field
import json
import pickle
from pathlib import Path
from collections import defaultdict, deque
from abc import ABC, abstractmethod

from dojo.core.solvers.utils.journal import Journal, Node
from dojo.core.solvers.utils.metric import MetricValue
from dojo.utils.logger import get_logger


@dataclass
class TaskFeatures:
    """Features that characterize a task."""
    task_type: str
    data_size: int
    num_features: int
    target_type: str  # 'regression', 'classification', 'generation'
    complexity_score: float
    domain: str  # 'tabular', 'vision', 'nlp', 'audio'
    evaluation_metric: str
    time_limit: float
    additional_features: Dict[str, Any] = field(default_factory=dict)
    
    def to_vector(self) -> np.ndarray:
        """Convert task features to numerical vector."""
        # Create feature vector
        features = [
            self.data_size,
            self.num_features,
            self.complexity_score,
            self.time_limit
        ]
        
        # One-hot encode categorical features
        task_types = ['regression', 'classification', 'generation', 'optimization']
        features.extend([1.0 if self.target_type == t else 0.0 for t in task_types])
        
        domains = ['tabular', 'vision', 'nlp', 'audio', 'multimodal']
        features.extend([1.0 if self.domain == d else 0.0 for d in domains])
        
        metrics = ['accuracy', 'f1', 'mse', 'mae', 'auc', 'bleu']
        features.extend([1.0 if self.evaluation_metric == m else 0.0 for m in metrics])
        
        return np.array(features, dtype=np.float32)


@dataclass
class TaskExperience:
    """Experience from solving a task."""
    task_features: TaskFeatures
    solver_config: Dict[str, Any]
    performance_history: List[float]
    operator_usage: Dict[str, int]
    operator_success_rates: Dict[str, float]
    final_performance: float
    convergence_step: int
    total_time: float
    best_strategies: List[str]
    failure_modes: List[str]
    
    def get_success_score(self) -> float:
        """Calculate overall success score for this experience."""
        # Combine final performance with convergence speed
        performance_score = self.final_performance
        convergence_score = 1.0 / (1.0 + self.convergence_step / 100.0)
        return 0.7 * performance_score + 0.3 * convergence_score


@dataclass
class MetaLearnerConfig:
    """Configuration for meta-learner."""
    feature_dim: int = 64
    hidden_dim: int = 128
    num_layers: int = 3
    learning_rate: float = 1e-3
    batch_size: int = 32
    memory_size: int = 1000
    similarity_threshold: float = 0.8
    adaptation_steps: int = 5
    meta_learning_rate: float = 1e-4
    use_maml: bool = True  # Model-Agnostic Meta-Learning
    use_prototypical: bool = False  # Prototypical Networks


class TaskSimilarityCalculator:
    """Calculate similarity between tasks."""
    
    def __init__(self, weights: Optional[Dict[str, float]] = None):
        self.weights = weights or {
            'domain': 0.3,
            'target_type': 0.2,
            'data_size': 0.2,
            'complexity': 0.2,
            'metric': 0.1
        }
    
    def calculate_similarity(self, task1: TaskFeatures, task2: TaskFeatures) -> float:
        """Calculate similarity score between two tasks."""
        similarity = 0.0
        
        # Domain similarity
        if task1.domain == task2.domain:
            similarity += self.weights['domain']
        
        # Target type similarity
        if task1.target_type == task2.target_type:
            similarity += self.weights['target_type']
        
        # Data size similarity (normalized)
        size_diff = abs(np.log10(task1.data_size + 1) - np.log10(task2.data_size + 1))
        size_sim = max(0, 1 - size_diff / 5.0)  # Normalize to [0,1]
        similarity += self.weights['data_size'] * size_sim
        
        # Complexity similarity
        complexity_diff = abs(task1.complexity_score - task2.complexity_score)
        complexity_sim = max(0, 1 - complexity_diff)
        similarity += self.weights['complexity'] * complexity_sim
        
        # Metric similarity
        if task1.evaluation_metric == task2.evaluation_metric:
            similarity += self.weights['metric']
        
        return similarity


class MetaNetwork(nn.Module):
    """Neural network for meta-learning."""
    
    def __init__(self, input_dim: int, hidden_dim: int, output_dim: int, num_layers: int):
        super().__init__()
        
        layers = []
        current_dim = input_dim
        
        for i in range(num_layers):
            layers.append(nn.Linear(current_dim, hidden_dim))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(0.1))
            current_dim = hidden_dim
        
        layers.append(nn.Linear(current_dim, output_dim))
        
        self.network = nn.Sequential(*layers)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.network(x)


class MetaLearner:
    """Meta-learning system for cross-task knowledge transfer."""
    
    def __init__(self, config: MetaLearnerConfig):
        self.config = config
        self.logger = get_logger()
        
        # Task experience database
        self.task_experiences: List[TaskExperience] = []
        self.task_embeddings: List[np.ndarray] = []
        
        # Similarity calculator
        self.similarity_calc = TaskSimilarityCalculator()
        
        # Meta-learning models
        self.strategy_predictor = MetaNetwork(
            input_dim=20,  # Task feature vector size
            hidden_dim=config.hidden_dim,
            output_dim=10,  # Number of strategy types
            num_layers=config.num_layers
        )
        
        self.hyperparameter_predictor = MetaNetwork(
            input_dim=20,
            hidden_dim=config.hidden_dim,
            output_dim=5,  # Number of hyperparameters to predict
            num_layers=config.num_layers
        )
        
        # Optimizers
        self.strategy_optimizer = optim.Adam(
            self.strategy_predictor.parameters(),
            lr=config.learning_rate
        )
        self.hyperparam_optimizer = optim.Adam(
            self.hyperparameter_predictor.parameters(),
            lr=config.learning_rate
        )
        
        # Strategy mapping
        self.strategies = [
            'exploration_heavy', 'exploitation_heavy', 'balanced',
            'diversity_focused', 'convergence_focused', 'adaptive',
            'multi_objective', 'robust', 'fast', 'thorough'
        ]
    
    def add_task_experience(self, experience: TaskExperience):
        """Add experience from solving a task."""
        self.task_experiences.append(experience)
        
        # Create task embedding
        task_vector = experience.task_features.to_vector()
        self.task_embeddings.append(task_vector)
        
        # Update meta-models
        self._update_meta_models()
        
        self.logger.info(f"Added task experience. Total experiences: {len(self.task_experiences)}")
    
    def predict_strategy(self, task_features: TaskFeatures) -> Dict[str, float]:
        """Predict optimal strategy for a new task."""
        task_vector = task_features.to_vector()
        
        # Find similar tasks
        similar_tasks = self._find_similar_tasks(task_features)
        
        if not similar_tasks:
            # No similar tasks, use default strategy
            return {'balanced': 1.0}
        
        # Use meta-model to predict strategy
        with torch.no_grad():
            task_tensor = torch.FloatTensor(task_vector).unsqueeze(0)
            strategy_logits = self.strategy_predictor(task_tensor)
            strategy_probs = torch.softmax(strategy_logits, dim=1).squeeze().numpy()
        
        # Combine with similar task strategies
        strategy_scores = {}
        for i, strategy in enumerate(self.strategies):
            meta_score = strategy_probs[i]
            
            # Weight by similar task performance
            similar_score = 0.0
            total_weight = 0.0
            
            for exp, similarity in similar_tasks:
                if strategy in exp.best_strategies:
                    weight = similarity * exp.get_success_score()
                    similar_score += weight
                    total_weight += weight
            
            if total_weight > 0:
                similar_score /= total_weight
            
            # Combine scores
            strategy_scores[strategy] = 0.6 * meta_score + 0.4 * similar_score
        
        return strategy_scores
    
    def predict_hyperparameters(self, task_features: TaskFeatures) -> Dict[str, float]:
        """Predict optimal hyperparameters for a new task."""
        task_vector = task_features.to_vector()
        
        # Find similar tasks
        similar_tasks = self._find_similar_tasks(task_features)
        
        if not similar_tasks:
            # Default hyperparameters
            return {
                'learning_rate': 1e-3,
                'temperature': 1.0,
                'exploration_weight': 1.0,
                'batch_size': 32,
                'patience': 10
            }
        
        # Use meta-model to predict hyperparameters
        with torch.no_grad():
            task_tensor = torch.FloatTensor(task_vector).unsqueeze(0)
            hyperparam_values = self.hyperparameter_predictor(task_tensor).squeeze().numpy()
        
        # Map to actual hyperparameter values
        predicted_params = {
            'learning_rate': np.exp(hyperparam_values[0] * 5 - 10),  # Log scale
            'temperature': np.exp(hyperparam_values[1] * 2 - 1),
            'exploration_weight': hyperparam_values[2] * 5,
            'batch_size': int(2 ** (hyperparam_values[3] * 4 + 4)),  # Powers of 2
            'patience': int(hyperparam_values[4] * 20 + 5)
        }
        
        # Refine with similar task experiences
        if similar_tasks:
            refined_params = {}
            for param_name in predicted_params:
                values = []
                weights = []
                
                for exp, similarity in similar_tasks:
                    if param_name in exp.solver_config:
                        values.append(exp.solver_config[param_name])
                        weights.append(similarity * exp.get_success_score())
                
                if values:
                    # Weighted average
                    weighted_avg = np.average(values, weights=weights)
                    # Combine with prediction
                    refined_params[param_name] = (
                        0.7 * predicted_params[param_name] + 0.3 * weighted_avg
                    )
                else:
                    refined_params[param_name] = predicted_params[param_name]
            
            predicted_params = refined_params
        
        self.logger.debug(f"Predicted hyperparameters: {predicted_params}")
        return predicted_params
    
    def get_operator_recommendations(self, task_features: TaskFeatures) -> Dict[str, float]:
        """Get operator usage recommendations based on similar tasks."""
        similar_tasks = self._find_similar_tasks(task_features)
        
        if not similar_tasks:
            # Default equal weights
            return {'draft': 0.25, 'improve': 0.25, 'debug': 0.25, 'analyze': 0.25}
        
        # Aggregate operator success rates from similar tasks
        operator_scores = defaultdict(list)
        
        for exp, similarity in similar_tasks:
            weight = similarity * exp.get_success_score()
            for op, success_rate in exp.operator_success_rates.items():
                operator_scores[op].append((success_rate, weight))
        
        # Calculate weighted averages
        recommendations = {}
        total_weight = 0.0
        
        for op, scores_weights in operator_scores.items():
            if scores_weights:
                weighted_score = sum(score * weight for score, weight in scores_weights)
                total_weight_op = sum(weight for _, weight in scores_weights)
                recommendations[op] = weighted_score / total_weight_op
                total_weight += recommendations[op]
        
        # Normalize to probabilities
        if total_weight > 0:
            for op in recommendations:
                recommendations[op] /= total_weight
        else:
            # Fallback to uniform
            ops = ['draft', 'improve', 'debug', 'analyze']
            recommendations = {op: 1.0 / len(ops) for op in ops}
        
        return recommendations
    
    def _find_similar_tasks(self, task_features: TaskFeatures, 
                           top_k: int = 5) -> List[Tuple[TaskExperience, float]]:
        """Find most similar tasks to the given task features."""
        similarities = []
        
        for exp in self.task_experiences:
            similarity = self.similarity_calc.calculate_similarity(
                task_features, exp.task_features
            )
            if similarity >= self.config.similarity_threshold:
                similarities.append((exp, similarity))
        
        # Sort by similarity and return top-k
        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities[:top_k]
    
    def _update_meta_models(self):
        """Update meta-learning models with new experience."""
        if len(self.task_experiences) < self.config.batch_size:
            return
        
        # Prepare training data
        X = []
        y_strategy = []
        y_hyperparam = []
        
        for exp in self.task_experiences[-self.config.batch_size:]:
            task_vector = exp.task_features.to_vector()
            X.append(task_vector)
            
            # Strategy labels (one-hot for best strategies)
            strategy_label = np.zeros(len(self.strategies))
            for strategy in exp.best_strategies:
                if strategy in self.strategies:
                    idx = self.strategies.index(strategy)
                    strategy_label[idx] = 1.0
            y_strategy.append(strategy_label)
            
            # Hyperparameter labels (normalized)
            hyperparam_label = [
                np.log(exp.solver_config.get('learning_rate', 1e-3)) / 5 + 2,
                np.log(exp.solver_config.get('temperature', 1.0)) + 1,
                exp.solver_config.get('exploration_weight', 1.0) / 5,
                np.log2(exp.solver_config.get('batch_size', 32)) - 4,
                (exp.solver_config.get('patience', 10) - 5) / 20
            ]
            y_hyperparam.append(hyperparam_label)
        
        # Convert to tensors
        X_tensor = torch.FloatTensor(X)
        y_strategy_tensor = torch.FloatTensor(y_strategy)
        y_hyperparam_tensor = torch.FloatTensor(y_hyperparam)
        
        # Train strategy predictor
        self.strategy_optimizer.zero_grad()
        strategy_pred = self.strategy_predictor(X_tensor)
        strategy_loss = nn.BCEWithLogitsLoss()(strategy_pred, y_strategy_tensor)
        strategy_loss.backward()
        self.strategy_optimizer.step()
        
        # Train hyperparameter predictor
        self.hyperparam_optimizer.zero_grad()
        hyperparam_pred = self.hyperparameter_predictor(X_tensor)
        hyperparam_loss = nn.MSELoss()(hyperparam_pred, y_hyperparam_tensor)
        hyperparam_loss.backward()
        self.hyperparam_optimizer.step()
        
        self.logger.debug(f"Meta-model updated. Strategy loss: {strategy_loss.item():.4f}, "
                         f"Hyperparam loss: {hyperparam_loss.item():.4f}")
    
    def save_knowledge_base(self, path: str):
        """Save the accumulated knowledge base."""
        knowledge_base = {
            'task_experiences': [
                {
                    'task_features': exp.task_features.__dict__,
                    'solver_config': exp.solver_config,
                    'performance_history': exp.performance_history,
                    'operator_usage': exp.operator_usage,
                    'operator_success_rates': exp.operator_success_rates,
                    'final_performance': exp.final_performance,
                    'convergence_step': exp.convergence_step,
                    'total_time': exp.total_time,
                    'best_strategies': exp.best_strategies,
                    'failure_modes': exp.failure_modes
                }
                for exp in self.task_experiences
            ],
            'config': self.config.__dict__
        }
        
        # Save JSON data
        with open(f"{path}.json", 'w') as f:
            json.dump(knowledge_base, f, indent=2, default=str)
        
        # Save model states
        torch.save({
            'strategy_predictor': self.strategy_predictor.state_dict(),
            'hyperparameter_predictor': self.hyperparameter_predictor.state_dict(),
            'strategy_optimizer': self.strategy_optimizer.state_dict(),
            'hyperparam_optimizer': self.hyperparam_optimizer.state_dict()
        }, f"{path}_models.pt")
        
        self.logger.info(f"Knowledge base saved to {path}")
    
    def load_knowledge_base(self, path: str):
        """Load accumulated knowledge base."""
        # Load JSON data
        with open(f"{path}.json", 'r') as f:
            knowledge_base = json.load(f)
        
        # Reconstruct task experiences
        self.task_experiences = []
        for exp_data in knowledge_base['task_experiences']:
            task_features = TaskFeatures(**exp_data['task_features'])
            experience = TaskExperience(
                task_features=task_features,
                solver_config=exp_data['solver_config'],
                performance_history=exp_data['performance_history'],
                operator_usage=exp_data['operator_usage'],
                operator_success_rates=exp_data['operator_success_rates'],
                final_performance=exp_data['final_performance'],
                convergence_step=exp_data['convergence_step'],
                total_time=exp_data['total_time'],
                best_strategies=exp_data['best_strategies'],
                failure_modes=exp_data['failure_modes']
            )
            self.task_experiences.append(experience)
        
        # Rebuild embeddings
        self.task_embeddings = [exp.task_features.to_vector() for exp in self.task_experiences]
        
        # Load model states
        if Path(f"{path}_models.pt").exists():
            checkpoint = torch.load(f"{path}_models.pt", map_location='cpu')
            self.strategy_predictor.load_state_dict(checkpoint['strategy_predictor'])
            self.hyperparameter_predictor.load_state_dict(checkpoint['hyperparameter_predictor'])
            self.strategy_optimizer.load_state_dict(checkpoint['strategy_optimizer'])
            self.hyperparam_optimizer.load_state_dict(checkpoint['hyperparam_optimizer'])
        
        self.logger.info(f"Knowledge base loaded from {path}. "
                        f"Total experiences: {len(self.task_experiences)}")


def extract_task_features_from_mlebench(task_name: str, task_info: Dict[str, Any]) -> TaskFeatures:
    """Extract task features from MLE-bench task information."""
    return TaskFeatures(
        task_type=task_name,
        data_size=task_info.get('data_size', 1000),
        num_features=task_info.get('num_features', 10),
        target_type='regression' if 'regression' in task_name.lower() else 'classification',
        complexity_score=task_info.get('complexity', 0.5),
        domain='tabular',
        evaluation_metric=task_info.get('metric', 'accuracy'),
        time_limit=task_info.get('time_limit', 3600),
        additional_features=task_info
    )
