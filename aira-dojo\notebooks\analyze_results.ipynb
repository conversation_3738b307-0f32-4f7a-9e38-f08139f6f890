{"cells": [{"cell_type": "markdown", "id": "8c0c8378", "metadata": {}, "source": ["# Basic Tools to Analyze Results\n", "\n"]}, {"cell_type": "markdown", "id": "00003720", "metadata": {}, "source": ["In this notebook, we will introduce you to the basic tools for analyzing the results of your experiments. We will examine the outcomes from AIRA<sub>GREEDY</sub>, AIDE<sub>GREEDY</sub>, AIRA<sub>MCTS</sub>, and AIRA<sub>EVO</sub>. You can find the commands to run these experiments in the [README.md](../README.md) under the \"Example Usage\" subsection."]}, {"cell_type": "markdown", "id": "59492a99", "metadata": {}, "source": ["## Load Exeriments"]}, {"cell_type": "code", "execution_count": null, "id": "17a715af", "metadata": {}, "outputs": [], "source": ["from dojo.utils.environment import get_log_dir\n", "from dojo.analysis_utils.meta_data_wrangling import (\n", "    collect_all_meta_experiments_in_one_df,\n", "    format_experiment_data,\n", "    filter_dataframe_based_on_data_validity,\n", "    add_node_elapsed_from_first\n", ")\n", "import os\n", "import pandas as pd\n", "from collections.abc import Iterable\n", "\n", "user = os.getenv(\"USER\")\n", "\n", "\n", "def extract_best_node_content(row):\n", "    best_idx = row['best_node_idx']\n", "    if pd.isna(best_idx):\n", "        return row\n", "    return row.apply(\n", "        lambda x: x[int(best_idx)] if isinstance(x, Iterable) and not isinstance(x, str) and int(best_idx) < len(x) else x\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "41f1d550", "metadata": {}, "outputs": [], "source": ["# Dictonary with paths to meta-experiments and their corresponding methods (for methods, you can choose any name you like)\n", "methods = {\n", "    f\"{get_log_dir()}/aira-dojo/user_{user}_issue_AIDE_GREEDY_o3\": \"AIDE_GREEDY\",\n", "    f\"{get_log_dir()}/aira-dojo/user_{user}_issue_AIRA_GREEDY_o3\": \"AIRA_GREEDY\",\n", "    f\"{get_log_dir()}/aira-dojo/user_{user}_issue_AIRA_EVO_o3\": \"AIRA_EVO\",\n", "    f\"{get_log_dir()}/aira-dojo/user_{user}_issue_AIRA_GREEDY_o3\": \"AIRA_MCTS(c=0.25)\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "ea05570d", "metadata": {}, "outputs": [], "source": ["df = collect_all_meta_experiments_in_one_df(\n", "    meta_experiment_paths=list(methods.keys()),\n", "    path_to_method_name=methods,\n", ")\n", "df = filter_dataframe_based_on_data_validity(df)\n", "\n"]}, {"cell_type": "markdown", "id": "2f833d30", "metadata": {}, "source": ["## Getting Statistics from the Results"]}, {"cell_type": "code", "execution_count": null, "id": "a8b979e2", "metadata": {}, "outputs": [], "source": ["df = add_node_elapsed_from_first(df)\n", "statistics_df , _ = format_experiment_data(df, select_using_test=True)"]}, {"cell_type": "markdown", "id": "99d987b7", "metadata": {}, "source": ["### Best Node based on Validation Score"]}, {"cell_type": "code", "execution_count": null, "id": "13906013", "metadata": {}, "outputs": [], "source": ["df[\"best_node_idx\"] = statistics_df[\"expected_return_node\"]\n", "df.apply(extract_best_node_content, axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "91cf77d8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "aira-dojo-opensource", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 5}