# Copyright (c) Meta Platforms, Inc. and affiliates.
# All rights reserved.
#
# This source code is licensed under the license found in the
# LICENSE file in the root directory of this source tree.

"""
Meta-learning and adaptive search strategies for AIRA Dojo.

This module provides sophisticated meta-learning capabilities including:
- Reinforcement Learning-based operator selection
- Bayesian Optimization for hyperparameter tuning
- Neural Architecture Search integration
- Meta-learning across tasks
- Adaptive temperature scheduling
"""

from .rl_search_policy import RLSearchPolicy
from .bayesian_optimizer import BayesianOptimizer
from .nas_integration import NASIntegration
from .meta_learner import Meta<PERSON><PERSON>ner
from .adaptive_scheduler import AdaptiveScheduler

__all__ = [
    "RLSearchPolicy",
    "BayesianOptimizer", 
    "NASIntegration",
    "MetaLearner",
    "AdaptiveScheduler"
]
