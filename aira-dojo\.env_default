PRIMARY_KEY="<<<AZURE_PRIMARY_KEY>>>" # or OPENAI_API_KEY if you are not using azure

# Specific keys:

PRIMARY_KEY_O1_PREVIEW="<<<AZURE_PRIMARY_KEY_O1>>>" # or OPENAI_API_KEY if you are not using azure
HOST_O1_PREVIEW="<<<AZURE_HOST_O1>>>" # e.g., "https://azure-services-..."
 
PRIMARY_KEY_O1="<<<AZURE_KEY_FOR_O1>>>"
PRIMARY_KEY_O3_MINI="<<<AZURE_KEY_FOR_O3_MINI>>>"
PRIMARY_KEY_O3="<<<AZURE_KEY_FOR_O3>>>"
PRIMARY_KEY="<<<AZURE_KEY_FOR_DEFAULT>>>"
# or OPENAI_API_KEY if you are not using azure

# Change these paths to a suitable location on your system
# !! IMPORTANT Absolute paths only !!
# path where experiment logs will be stored
LOGGING_DIR="/<<<PATH_TO_TEAM_STORAGE>>>/shared/logs/"
# path to mlebench data (see src/dojo/tasks/mlebench/README.md for instructions)
MLE_BENCH_DATA_DIR="/<<<PATH_TO_TEAM_STORAGE>>>/shared/cache/dojo/tasks/mlebench/"
# path to superimage directory (see docs/BUILD_SUPERIMAGE.md for instructions)
SUPERIMAGE_DIR="/<<<PATH_TO_TEAM_STORAGE>>>/shared/sif/"
# default slurm accout (used for slurm jobs)
DEFAULT_SLURM_ACCOUNT="<<<YOUR_SLURM_ACCOUNT>>>"
# default slurm partition (used for slurm jobs)
DEFAULT_SLURM_PARTITION="<<<YOUR_SLURM_PARTITION>>>"
# default slurm qos (used for slurm jobs
DEFAULT_SLURM_QOS="<<<YOUR_SLURM_QOS>>>"
