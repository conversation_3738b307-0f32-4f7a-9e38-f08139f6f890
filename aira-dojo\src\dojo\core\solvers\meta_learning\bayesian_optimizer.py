# Copyright (c) Meta Platforms, Inc. and affiliates.
# All rights reserved.
#
# This source code is licensed under the license found in the
# LICENSE file in the root directory of this source tree.

"""
Bayesian Optimization for hyperparameter and architecture search.

This module provides Bayesian optimization capabilities for automatically
tuning solver hyperparameters and neural architecture parameters.
"""

import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass, field
import json
from pathlib import Path
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import <PERSON><PERSON>, RB<PERSON>, WhiteKernel
from scipy.optimize import minimize
from scipy.stats import norm
import warnings
warnings.filterwarnings('ignore')

from dojo.utils.logger import get_logger


@dataclass
class ParameterSpace:
    """Defines a parameter space for optimization."""
    name: str
    param_type: str  # 'continuous', 'discrete', 'categorical'
    bounds: Optional[Tuple[float, float]] = None  # For continuous
    choices: Optional[List[Any]] = None  # For discrete/categorical
    log_scale: bool = False  # Whether to use log scale for continuous params
    
    def sample_random(self) -> Any:
        """Sample a random value from this parameter space."""
        if self.param_type == 'continuous':
            if self.log_scale:
                log_low, log_high = np.log(self.bounds[0]), np.log(self.bounds[1])
                return np.exp(np.random.uniform(log_low, log_high))
            else:
                return np.random.uniform(self.bounds[0], self.bounds[1])
        elif self.param_type in ['discrete', 'categorical']:
            return np.random.choice(self.choices)
        else:
            raise ValueError(f"Unknown parameter type: {self.param_type}")
    
    def normalize(self, value: Any) -> float:
        """Normalize value to [0, 1] range for GP."""
        if self.param_type == 'continuous':
            if self.log_scale:
                log_val = np.log(value)
                log_low, log_high = np.log(self.bounds[0]), np.log(self.bounds[1])
                return (log_val - log_low) / (log_high - log_low)
            else:
                return (value - self.bounds[0]) / (self.bounds[1] - self.bounds[0])
        elif self.param_type in ['discrete', 'categorical']:
            return self.choices.index(value) / (len(self.choices) - 1)
        else:
            raise ValueError(f"Unknown parameter type: {self.param_type}")
    
    def denormalize(self, normalized_value: float) -> Any:
        """Convert normalized value back to original space."""
        if self.param_type == 'continuous':
            if self.log_scale:
                log_low, log_high = np.log(self.bounds[0]), np.log(self.bounds[1])
                log_val = log_low + normalized_value * (log_high - log_low)
                return np.exp(log_val)
            else:
                return self.bounds[0] + normalized_value * (self.bounds[1] - self.bounds[0])
        elif self.param_type in ['discrete', 'categorical']:
            idx = int(np.round(normalized_value * (len(self.choices) - 1)))
            return self.choices[max(0, min(idx, len(self.choices) - 1))]
        else:
            raise ValueError(f"Unknown parameter type: {self.param_type}")


@dataclass
class BayesianOptimizerConfig:
    """Configuration for Bayesian Optimizer."""
    n_initial_points: int = 10
    acquisition_function: str = 'ei'  # 'ei', 'pi', 'ucb'
    xi: float = 0.01  # Exploration parameter for EI/PI
    kappa: float = 2.576  # Exploration parameter for UCB
    kernel_type: str = 'matern'  # 'matern', 'rbf'
    alpha: float = 1e-6  # Noise level
    n_restarts_optimizer: int = 5
    max_iterations: int = 100
    convergence_tolerance: float = 1e-6
    save_history: bool = True


class BayesianOptimizer:
    """Bayesian Optimizer for hyperparameter and architecture search."""
    
    def __init__(self, parameter_spaces: List[ParameterSpace], 
                 config: BayesianOptimizerConfig,
                 checkpoint_path: Optional[str] = None):
        self.parameter_spaces = parameter_spaces
        self.config = config
        self.logger = get_logger()
        
        # Initialize Gaussian Process
        self._setup_gaussian_process()
        
        # Optimization history
        self.X_observed = []  # Normalized parameter values
        self.y_observed = []  # Objective values
        self.param_history = []  # Original parameter values
        self.iteration = 0
        
        # Best found configuration
        self.best_params = None
        self.best_score = float('-inf')
        
        # Load checkpoint if provided
        if checkpoint_path and Path(checkpoint_path).exists():
            self.load_checkpoint(checkpoint_path)
    
    def _setup_gaussian_process(self):
        """Setup the Gaussian Process model."""
        if self.config.kernel_type == 'matern':
            kernel = Matern(length_scale=1.0, nu=2.5)
        elif self.config.kernel_type == 'rbf':
            kernel = RBF(length_scale=1.0)
        else:
            raise ValueError(f"Unknown kernel type: {self.config.kernel_type}")
        
        # Add white noise kernel
        kernel = kernel + WhiteKernel(noise_level=self.config.alpha)
        
        self.gp = GaussianProcessRegressor(
            kernel=kernel,
            alpha=self.config.alpha,
            n_restarts_optimizer=self.config.n_restarts_optimizer,
            normalize_y=True
        )
    
    def suggest_parameters(self) -> Dict[str, Any]:
        """Suggest next set of parameters to evaluate."""
        if len(self.X_observed) < self.config.n_initial_points:
            # Random sampling for initial points
            params = self._sample_random_parameters()
        else:
            # Bayesian optimization
            params = self._optimize_acquisition()
        
        self.logger.debug(f"BO suggested parameters: {params}")
        return params
    
    def _sample_random_parameters(self) -> Dict[str, Any]:
        """Sample random parameters from the parameter spaces."""
        params = {}
        for param_space in self.parameter_spaces:
            params[param_space.name] = param_space.sample_random()
        return params
    
    def _optimize_acquisition(self) -> Dict[str, Any]:
        """Optimize acquisition function to find next parameters."""
        # Fit GP to observed data
        if len(self.X_observed) > 0:
            X = np.array(self.X_observed)
            y = np.array(self.y_observed)
            self.gp.fit(X, y)
        
        # Optimize acquisition function
        best_x = None
        best_acq = float('-inf')
        
        # Multi-start optimization
        for _ in range(self.config.n_restarts_optimizer):
            # Random starting point
            x0 = np.random.uniform(0, 1, len(self.parameter_spaces))
            
            # Optimize
            result = minimize(
                fun=lambda x: -self._acquisition_function(x.reshape(1, -1)),
                x0=x0,
                bounds=[(0, 1)] * len(self.parameter_spaces),
                method='L-BFGS-B'
            )
            
            if result.success and -result.fun > best_acq:
                best_acq = -result.fun
                best_x = result.x
        
        # Convert back to original parameter space
        if best_x is None:
            # Fallback to random sampling
            return self._sample_random_parameters()
        
        params = {}
        for i, param_space in enumerate(self.parameter_spaces):
            params[param_space.name] = param_space.denormalize(best_x[i])
        
        return params
    
    def _acquisition_function(self, X: np.ndarray) -> np.ndarray:
        """Compute acquisition function values."""
        if len(self.X_observed) == 0:
            return np.zeros(X.shape[0])
        
        # Get GP predictions
        mu, sigma = self.gp.predict(X, return_std=True)
        sigma = np.maximum(sigma, 1e-9)  # Avoid division by zero
        
        if self.config.acquisition_function == 'ei':
            # Expected Improvement
            f_best = np.max(self.y_observed)
            z = (mu - f_best - self.config.xi) / sigma
            ei = (mu - f_best - self.config.xi) * norm.cdf(z) + sigma * norm.pdf(z)
            return ei
        
        elif self.config.acquisition_function == 'pi':
            # Probability of Improvement
            f_best = np.max(self.y_observed)
            z = (mu - f_best - self.config.xi) / sigma
            return norm.cdf(z)
        
        elif self.config.acquisition_function == 'ucb':
            # Upper Confidence Bound
            return mu + self.config.kappa * sigma
        
        else:
            raise ValueError(f"Unknown acquisition function: {self.config.acquisition_function}")
    
    def update(self, parameters: Dict[str, Any], objective_value: float):
        """Update optimizer with new observation."""
        # Normalize parameters
        normalized_params = []
        for param_space in self.parameter_spaces:
            value = parameters[param_space.name]
            normalized_params.append(param_space.normalize(value))
        
        # Store observation
        self.X_observed.append(normalized_params)
        self.y_observed.append(objective_value)
        self.param_history.append(parameters.copy())
        
        # Update best configuration
        if objective_value > self.best_score:
            self.best_score = objective_value
            self.best_params = parameters.copy()
            self.logger.info(f"BO found new best: {self.best_score:.4f} with params {self.best_params}")
        
        self.iteration += 1
    
    def get_best_parameters(self) -> Tuple[Dict[str, Any], float]:
        """Get the best parameters found so far."""
        return self.best_params, self.best_score
    
    def get_optimization_history(self) -> List[Dict[str, Any]]:
        """Get the full optimization history."""
        history = []
        for i, (params, score) in enumerate(zip(self.param_history, self.y_observed)):
            history.append({
                'iteration': i,
                'parameters': params,
                'objective_value': score,
                'is_best': score == self.best_score
            })
        return history
    
    def save_checkpoint(self, path: str):
        """Save optimizer state to checkpoint."""
        checkpoint = {
            'X_observed': self.X_observed,
            'y_observed': self.y_observed,
            'param_history': self.param_history,
            'best_params': self.best_params,
            'best_score': self.best_score,
            'iteration': self.iteration,
            'config': self.config.__dict__,
            'parameter_spaces': [
                {
                    'name': ps.name,
                    'param_type': ps.param_type,
                    'bounds': ps.bounds,
                    'choices': ps.choices,
                    'log_scale': ps.log_scale
                }
                for ps in self.parameter_spaces
            ]
        }
        
        with open(path, 'w') as f:
            json.dump(checkpoint, f, indent=2, default=str)
        
        self.logger.info(f"BO checkpoint saved to {path}")
    
    def load_checkpoint(self, path: str):
        """Load optimizer state from checkpoint."""
        with open(path, 'r') as f:
            checkpoint = json.load(f)
        
        self.X_observed = checkpoint['X_observed']
        self.y_observed = checkpoint['y_observed']
        self.param_history = checkpoint['param_history']
        self.best_params = checkpoint['best_params']
        self.best_score = checkpoint['best_score']
        self.iteration = checkpoint['iteration']
        
        self.logger.info(f"BO checkpoint loaded from {path}")


def create_solver_parameter_spaces() -> List[ParameterSpace]:
    """Create parameter spaces for common solver hyperparameters."""
    return [
        ParameterSpace(
            name='learning_rate',
            param_type='continuous',
            bounds=(1e-5, 1e-1),
            log_scale=True
        ),
        ParameterSpace(
            name='temperature',
            param_type='continuous',
            bounds=(0.1, 2.0)
        ),
        ParameterSpace(
            name='exploration_weight',
            param_type='continuous',
            bounds=(0.1, 5.0)
        ),
        ParameterSpace(
            name='batch_size',
            param_type='discrete',
            choices=[16, 32, 64, 128]
        ),
        ParameterSpace(
            name='optimizer_type',
            param_type='categorical',
            choices=['adam', 'sgd', 'rmsprop']
        )
    ]


def create_architecture_parameter_spaces() -> List[ParameterSpace]:
    """Create parameter spaces for neural architecture search."""
    return [
        ParameterSpace(
            name='num_layers',
            param_type='discrete',
            choices=[2, 3, 4, 5, 6]
        ),
        ParameterSpace(
            name='hidden_dim',
            param_type='discrete',
            choices=[64, 128, 256, 512]
        ),
        ParameterSpace(
            name='activation',
            param_type='categorical',
            choices=['relu', 'tanh', 'gelu', 'swish']
        ),
        ParameterSpace(
            name='dropout_rate',
            param_type='continuous',
            bounds=(0.0, 0.5)
        ),
        ParameterSpace(
            name='attention_heads',
            param_type='discrete',
            choices=[1, 2, 4, 8]
        )
    ]
