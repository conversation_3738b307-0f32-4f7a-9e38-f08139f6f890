# Copyright (c) Meta Platforms, Inc. and affiliates.
# All rights reserved.
#
# This source code is licensed under the license found in the
# LICENSE file in the root directory of this source tree.

"""
Adaptive Temperature Scheduling for dynamic exploration-exploitation balance.

This module provides sophisticated scheduling mechanisms that adapt based on
search progress, performance trends, and task characteristics.
"""

import numpy as np
import math
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass
from abc import ABC, abstractmethod
from collections import deque

from dojo.core.solvers.utils.journal import Journal, Node
from dojo.utils.logger import get_logger


@dataclass
class SchedulerConfig:
    """Configuration for adaptive schedulers."""
    initial_temp: float = 1.0
    final_temp: float = 0.1
    adaptation_rate: float = 0.1
    window_size: int = 10
    min_temp: float = 0.01
    max_temp: float = 5.0
    patience: int = 5
    improvement_threshold: float = 0.01


class BaseScheduler(ABC):
    """Base class for temperature schedulers."""
    
    def __init__(self, config: SchedulerConfig):
        self.config = config
        self.logger = get_logger()
        self.current_temp = config.initial_temp
        self.step_count = 0
        
    @abstractmethod
    def update(self, journal: Journal, task_info: Dict[str, Any]) -> float:
        """Update temperature based on current state."""
        pass
    
    def get_temperature(self) -> float:
        """Get current temperature."""
        return self.current_temp
    
    def reset(self):
        """Reset scheduler state."""
        self.current_temp = self.config.initial_temp
        self.step_count = 0


class LinearScheduler(BaseScheduler):
    """Linear temperature decay scheduler."""
    
    def __init__(self, config: SchedulerConfig, total_steps: int):
        super().__init__(config)
        self.total_steps = total_steps
    
    def update(self, journal: Journal, task_info: Dict[str, Any]) -> float:
        """Linear decay from initial to final temperature."""
        progress = min(self.step_count / self.total_steps, 1.0)
        self.current_temp = self.config.initial_temp * (1 - progress) + self.config.final_temp * progress
        self.step_count += 1
        return self.current_temp


class ExponentialScheduler(BaseScheduler):
    """Exponential temperature decay scheduler."""
    
    def __init__(self, config: SchedulerConfig, decay_rate: float = 0.95):
        super().__init__(config)
        self.decay_rate = decay_rate
    
    def update(self, journal: Journal, task_info: Dict[str, Any]) -> float:
        """Exponential decay of temperature."""
        self.current_temp = max(
            self.current_temp * self.decay_rate,
            self.config.final_temp
        )
        self.step_count += 1
        return self.current_temp


class CosineScheduler(BaseScheduler):
    """Cosine annealing temperature scheduler."""
    
    def __init__(self, config: SchedulerConfig, total_steps: int):
        super().__init__(config)
        self.total_steps = total_steps
    
    def update(self, journal: Journal, task_info: Dict[str, Any]) -> float:
        """Cosine annealing schedule."""
        progress = min(self.step_count / self.total_steps, 1.0)
        self.current_temp = self.config.final_temp + 0.5 * (
            self.config.initial_temp - self.config.final_temp
        ) * (1 + math.cos(math.pi * progress))
        self.step_count += 1
        return self.current_temp


class AdaptivePerformanceScheduler(BaseScheduler):
    """Adaptive scheduler based on performance trends."""
    
    def __init__(self, config: SchedulerConfig):
        super().__init__(config)
        self.performance_history = deque(maxlen=config.window_size)
        self.no_improvement_count = 0
        self.best_performance = float('-inf')
    
    def update(self, journal: Journal, task_info: Dict[str, Any]) -> float:
        """Adapt temperature based on performance trends."""
        # Get current best performance
        best_node = journal.get_best_node()
        current_performance = (
            best_node.metric.value if best_node and best_node.metric and not best_node.metric.is_worst
            else float('-inf')
        )
        
        self.performance_history.append(current_performance)
        
        # Check for improvement
        if current_performance > self.best_performance + self.config.improvement_threshold:
            self.best_performance = current_performance
            self.no_improvement_count = 0
            # Decrease temperature (more exploitation)
            self.current_temp *= (1 - self.config.adaptation_rate)
        else:
            self.no_improvement_count += 1
            if self.no_improvement_count >= self.config.patience:
                # Increase temperature (more exploration)
                self.current_temp *= (1 + self.config.adaptation_rate)
                self.no_improvement_count = 0
        
        # Clamp temperature
        self.current_temp = np.clip(
            self.current_temp,
            self.config.min_temp,
            self.config.max_temp
        )
        
        self.step_count += 1
        self.logger.debug(f"Adaptive scheduler: temp={self.current_temp:.3f}, "
                         f"perf={current_performance:.3f}, no_improve={self.no_improvement_count}")
        
        return self.current_temp


class DiversityBasedScheduler(BaseScheduler):
    """Scheduler that adapts based on solution diversity."""
    
    def __init__(self, config: SchedulerConfig):
        super().__init__(config)
        self.diversity_history = deque(maxlen=config.window_size)
    
    def update(self, journal: Journal, task_info: Dict[str, Any]) -> float:
        """Adapt temperature based on solution diversity."""
        # Calculate diversity metric
        diversity = self._calculate_diversity(journal)
        self.diversity_history.append(diversity)
        
        if len(self.diversity_history) >= 2:
            # If diversity is decreasing, increase temperature
            recent_diversity = np.mean(list(self.diversity_history)[-3:])
            older_diversity = np.mean(list(self.diversity_history)[:-3]) if len(self.diversity_history) > 3 else recent_diversity
            
            if recent_diversity < older_diversity:
                # Diversity decreasing - increase exploration
                self.current_temp *= (1 + self.config.adaptation_rate)
            else:
                # Diversity stable/increasing - can reduce exploration
                self.current_temp *= (1 - self.config.adaptation_rate * 0.5)
        
        # Clamp temperature
        self.current_temp = np.clip(
            self.current_temp,
            self.config.min_temp,
            self.config.max_temp
        )
        
        self.step_count += 1
        return self.current_temp
    
    def _calculate_diversity(self, journal: Journal) -> float:
        """Calculate diversity of solutions in journal."""
        if len(journal.good_nodes) < 2:
            return 1.0
        
        # Simple diversity metric based on performance variance
        performances = [
            node.metric.value for node in journal.good_nodes
            if node.metric and not node.metric.is_worst
        ]
        
        if len(performances) < 2:
            return 1.0
        
        return np.std(performances) / (np.mean(performances) + 1e-8)


class MultiObjectiveScheduler(BaseScheduler):
    """Scheduler for multi-objective optimization scenarios."""
    
    def __init__(self, config: SchedulerConfig, objectives: List[str]):
        super().__init__(config)
        self.objectives = objectives
        self.pareto_history = deque(maxlen=config.window_size)
    
    def update(self, journal: Journal, task_info: Dict[str, Any]) -> float:
        """Adapt temperature based on Pareto front progress."""
        # Calculate Pareto front size
        pareto_size = self._calculate_pareto_front_size(journal)
        self.pareto_history.append(pareto_size)
        
        if len(self.pareto_history) >= 2:
            # If Pareto front is not growing, increase exploration
            recent_growth = np.mean(list(self.pareto_history)[-3:])
            older_growth = np.mean(list(self.pareto_history)[:-3]) if len(self.pareto_history) > 3 else recent_growth
            
            if recent_growth <= older_growth:
                self.current_temp *= (1 + self.config.adaptation_rate)
            else:
                self.current_temp *= (1 - self.config.adaptation_rate * 0.5)
        
        # Clamp temperature
        self.current_temp = np.clip(
            self.current_temp,
            self.config.min_temp,
            self.config.max_temp
        )
        
        self.step_count += 1
        return self.current_temp
    
    def _calculate_pareto_front_size(self, journal: Journal) -> int:
        """Calculate size of Pareto front."""
        # Simplified - just count non-dominated solutions
        good_nodes = journal.good_nodes
        if not good_nodes:
            return 0
        
        # For single objective, just return number of unique performance levels
        performances = set()
        for node in good_nodes:
            if node.metric and not node.metric.is_worst:
                performances.add(round(node.metric.value, 4))
        
        return len(performances)


class AdaptiveScheduler:
    """Meta-scheduler that combines multiple scheduling strategies."""
    
    def __init__(self, config: SchedulerConfig, strategy: str = 'performance',
                 total_steps: Optional[int] = None):
        self.config = config
        self.logger = get_logger()
        
        # Initialize base scheduler based on strategy
        if strategy == 'linear':
            if total_steps is None:
                raise ValueError("total_steps required for linear scheduler")
            self.scheduler = LinearScheduler(config, total_steps)
        elif strategy == 'exponential':
            self.scheduler = ExponentialScheduler(config)
        elif strategy == 'cosine':
            if total_steps is None:
                raise ValueError("total_steps required for cosine scheduler")
            self.scheduler = CosineScheduler(config, total_steps)
        elif strategy == 'performance':
            self.scheduler = AdaptivePerformanceScheduler(config)
        elif strategy == 'diversity':
            self.scheduler = DiversityBasedScheduler(config)
        elif strategy == 'multi_objective':
            self.scheduler = MultiObjectiveScheduler(config, ['performance'])
        else:
            raise ValueError(f"Unknown scheduler strategy: {strategy}")
        
        self.strategy = strategy
        self.temperature_history = []
    
    def update(self, journal: Journal, task_info: Dict[str, Any] = None) -> float:
        """Update temperature using the selected strategy."""
        if task_info is None:
            task_info = {}
        
        temperature = self.scheduler.update(journal, task_info)
        self.temperature_history.append(temperature)
        
        self.logger.debug(f"Temperature updated: {temperature:.4f} (strategy: {self.strategy})")
        return temperature
    
    def get_temperature(self) -> float:
        """Get current temperature."""
        return self.scheduler.get_temperature()
    
    def get_temperature_history(self) -> List[float]:
        """Get temperature history."""
        return self.temperature_history.copy()
    
    def reset(self):
        """Reset scheduler state."""
        self.scheduler.reset()
        self.temperature_history = []
    
    def save_state(self) -> Dict[str, Any]:
        """Save scheduler state."""
        return {
            'strategy': self.strategy,
            'current_temp': self.scheduler.current_temp,
            'step_count': self.scheduler.step_count,
            'temperature_history': self.temperature_history,
            'config': self.config.__dict__
        }
    
    def load_state(self, state: Dict[str, Any]):
        """Load scheduler state."""
        self.scheduler.current_temp = state['current_temp']
        self.scheduler.step_count = state['step_count']
        self.temperature_history = state['temperature_history']
