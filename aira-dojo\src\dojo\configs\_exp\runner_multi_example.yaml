# @package _global_
hydra:
  sweeper:
    params:
      solver: mlebench/greedy, mlebench/mcts, mlebench/evo

defaults:
  - override /benchmark: mlebench/dev
  - override /interpreter: jupyter


launcher:
  qos: lowest

metadata:
  git_issue_id: example # Ideally, this should be a number fetched from github issue when running an actual experiment.

solver:
  step_limit: 5

vars:
  metadata.seed: [1,2,3]