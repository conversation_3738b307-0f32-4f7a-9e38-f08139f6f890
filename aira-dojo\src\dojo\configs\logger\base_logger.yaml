_target_: dojo.config_dataclasses.logger.LoggerConfig

# --- Logging options ---
use_console: True # Whether to log to stdout.
use_wandb: True  # Whether to log to wandb.ai.
use_json: True # Whether to save files locally in JSON format

# --- Other logger kwargs ---
wandb_project_name: aira  # Project name in wandb.ai.
wandb_entity: aira-dojo # entity name in wandb.ai
tags: [] # Tags to add to the experiment.
detailed_logging: False  # having mean/std/min/max can clutter wandb so we make it optional
print_config: true # Whether to print the config to stdout.
write_env_vars: true # Whether to write the environment variables to the config file.
