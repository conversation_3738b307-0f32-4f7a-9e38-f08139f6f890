
defaults:
  - evo
  # Load Operators
  - /solver/operators@operators:
    - mlebench/aira_operators/debug
    - mlebench/aira_operators/draft
    - mlebench/aira_operators/improve
    - mlebench/aide_operators/analyze
    - mlebench/aira_operators/crossover
  - override memory: simple_memory  # Enables Memory operator
  - override memory@debug_memory: debug_memory # Enables Debug Memory operator

step_limit: 10000

num_islands: 1
max_island_size: 500
crossover_prob: 0.5
migration_prob: 0.0
initial_temp: 1.0
final_temp: 1.0
num_generations_till_migration: 999
num_generations_till_crossover: 2
num_generations: 100
individuals_per_generation: 5

max_debug_depth: 10
max_debug_time: 21600 # 6 hours
data_preview: True
use_test_score: False
use_complexity: False

operators:
  draft:
    llm:
      generation_kwargs:
        temperature: 0.6
        top_p: 0.95
  improve:
    llm:
      generation_kwargs:
        temperature: 0.6
        top_p: 0.95
  debug:
    llm:
      generation_kwargs:
        temperature: 0.6
        top_p: 0.95
  analyze:
    llm:
      generation_kwargs:
        temperature: 0.5
  crossover:
    llm:
      generation_kwargs:
        temperature: 0.5
