# Copyright (c) Meta Platforms, Inc. and affiliates.
# All rights reserved.
#
# This source code is licensed under the license found in the
# LICENSE file in the root directory of this source tree.

"""
Meta-Adaptive Solver with sophisticated meta-learning and adaptive search strategies.

This solver integrates:
- Reinforcement Learning-based operator selection
- Bayesian Optimization for hyperparameter tuning
- Neural Architecture Search integration
- Meta-learning across tasks
- Adaptive temperature scheduling
"""

from .meta_adaptive_solver import MetaAdaptiveSolver

__all__ = ["MetaAdaptiveSolver"]
