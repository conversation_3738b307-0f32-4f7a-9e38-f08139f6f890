defaults:
  - meta_adaptive
  - /solver/operators@operators:
    - mlebench/aira_operators/debug    # Enables debugging mode for operator prompts
    - mlebench/aira_operators/draft    # Enables draft mode for generating initial outputs
    - mlebench/aira_operators/improve  # Enables improvement mode for refining outputs
    - mlebench/aide_operators/analyze  # Enables evaluation mode for assessing outputs
  - override memory: simple_memory  # Enables Memory operator
  - override memory@debug_memory: debug_memory # Enables Debug Memory operator
    
# Specifies the Meta-Adaptive solver with meta-learning capabilities
_target_: dojo.config_dataclasses.solver.meta_adaptive.MetaAdaptiveSolverConfig

# Whether to export search results after execution
export_search_results: true

# --- Search Configuration ---
step_limit: 500 # Maximum number of nodes the search algorithm can explore
max_debug_depth: 10 # Maximum depth of debugging analysis
max_debug_time: 1e9 # disabled
data_preview: True # Whether to provide the agent with a preview of the data

# --- Meta-Learning Components ---
use_rl_policy: true # Enable RL-based operator selection
use_bayesian_optimization: true # Enable Bayesian optimization for hyperparameters
use_adaptive_scheduling: true # Enable adaptive temperature scheduling
use_meta_learning: true # Enable meta-learning from previous tasks
use_nas: false # Disable NAS for MLE-bench tasks

# --- RL Policy Configuration ---
rl_hidden_dim: 128
rl_learning_rate: 1e-3
rl_epsilon: 0.1
rl_checkpoint_path: "${logger.output_dir}/rl_policy_checkpoint.pt"

# --- Bayesian Optimization Configuration ---
bo_initial_points: 5
bo_max_iterations: 50
bo_acquisition: "ei"
bo_checkpoint_path: "${logger.output_dir}/bo_checkpoint.json"

# --- Adaptive Scheduling Configuration ---
initial_temp: 1.0
final_temp: 0.1
adaptation_rate: 0.1
scheduler_strategy: "performance"

# --- Meta-Learning Configuration ---
meta_hidden_dim: 128
meta_learning_rate: 1e-3
knowledge_base_path: "${logger.output_dir}/knowledge_base"

# --- Search Behavior ---
use_test_score: False
use_complexity: False
lower_is_better: False

# --- Advanced Features ---
enable_operator_adaptation: true
enable_dynamic_hyperparams: true
enable_cross_task_learning: true

# --- Performance Monitoring ---
performance_window_size: 10
early_stopping_patience: 20
min_improvement_threshold: 0.01
convergence_threshold: 0.001

# --- Operator Configuration ---
operators:
  draft:
    llm:
      generation_kwargs:
        temperature: 0.6
        top_p: 0.95
  improve:
    llm:
      generation_kwargs:
        temperature: 0.6
        top_p: 0.95
  debug:
    llm:
      generation_kwargs:
        temperature: 0.6
        top_p: 0.95
  analyze:
    llm:
      generation_kwargs:
        temperature: 0.5
