# Copyright (c) Meta Platforms, Inc. and affiliates.
# All rights reserved.
#
# This source code is licensed under the license found in the
# LICENSE file in the root directory of this source tree.

"""
Reinforcement Learning-based Search Policy for intelligent operator selection.

This module implements a deep RL agent that learns optimal operator selection
policies based on the current state of the search process.
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from collections import deque
import random
import json
from pathlib import Path

from dojo.core.solvers.utils.journal import Journal, Node
from dojo.core.solvers.utils.metric import MetricValue
from dojo.utils.logger import get_logger


@dataclass
class RLConfig:
    """Configuration for RL-based search policy."""
    state_dim: int = 64
    action_dim: int = 4  # draft, improve, debug, analyze
    hidden_dim: int = 128
    learning_rate: float = 1e-3
    epsilon: float = 0.1
    epsilon_decay: float = 0.995
    epsilon_min: float = 0.01
    memory_size: int = 10000
    batch_size: int = 32
    target_update_freq: int = 100
    reward_scale: float = 1.0
    use_double_dqn: bool = True
    use_dueling: bool = True


class DuelingDQN(nn.Module):
    """Dueling Deep Q-Network for operator selection."""
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dim: int):
        super().__init__()
        self.state_dim = state_dim
        self.action_dim = action_dim
        
        # Shared feature extractor
        self.feature_extractor = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU()
        )
        
        # Value stream
        self.value_stream = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, 1)
        )
        
        # Advantage stream
        self.advantage_stream = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, action_dim)
        )
    
    def forward(self, state: torch.Tensor) -> torch.Tensor:
        features = self.feature_extractor(state)
        value = self.value_stream(features)
        advantage = self.advantage_stream(features)
        
        # Dueling architecture: Q(s,a) = V(s) + A(s,a) - mean(A(s,a))
        q_values = value + advantage - advantage.mean(dim=1, keepdim=True)
        return q_values


class ReplayBuffer:
    """Experience replay buffer for RL training."""
    
    def __init__(self, capacity: int):
        self.buffer = deque(maxlen=capacity)
    
    def push(self, state: np.ndarray, action: int, reward: float, 
             next_state: np.ndarray, done: bool):
        """Add experience to buffer."""
        self.buffer.append((state, action, reward, next_state, done))
    
    def sample(self, batch_size: int) -> Tuple[torch.Tensor, ...]:
        """Sample batch of experiences."""
        batch = random.sample(self.buffer, batch_size)
        state, action, reward, next_state, done = map(np.array, zip(*batch))
        
        return (
            torch.FloatTensor(state),
            torch.LongTensor(action),
            torch.FloatTensor(reward),
            torch.FloatTensor(next_state),
            torch.BoolTensor(done)
        )
    
    def __len__(self) -> int:
        return len(self.buffer)


class RLSearchPolicy:
    """Reinforcement Learning-based search policy for operator selection."""
    
    def __init__(self, config: RLConfig, checkpoint_path: Optional[str] = None):
        self.config = config
        self.logger = get_logger()
        
        # Initialize networks
        self.q_network = DuelingDQN(config.state_dim, config.action_dim, config.hidden_dim)
        self.target_network = DuelingDQN(config.state_dim, config.action_dim, config.hidden_dim)
        self.optimizer = optim.Adam(self.q_network.parameters(), lr=config.learning_rate)
        
        # Initialize replay buffer
        self.replay_buffer = ReplayBuffer(config.memory_size)
        
        # Training state
        self.epsilon = config.epsilon
        self.step_count = 0
        self.episode_count = 0
        
        # Action mapping
        self.action_to_operator = {
            0: "draft",
            1: "improve", 
            2: "debug",
            3: "analyze"
        }
        self.operator_to_action = {v: k for k, v in self.action_to_operator.items()}
        
        # State tracking
        self.last_state = None
        self.last_action = None
        self.episode_rewards = []
        
        # Load checkpoint if provided
        if checkpoint_path and Path(checkpoint_path).exists():
            self.load_checkpoint(checkpoint_path)
        
        # Update target network
        self.update_target_network()
    
    def extract_state_features(self, journal: Journal, task_info: Dict[str, Any]) -> np.ndarray:
        """Extract state features from current search state."""
        features = np.zeros(self.config.state_dim)
        
        # Basic statistics
        features[0] = len(journal.nodes)
        features[1] = len(journal.good_nodes)
        features[2] = len(journal.buggy_nodes)
        features[3] = len(journal.draft_nodes)
        
        # Performance metrics
        best_node = journal.get_best_node()
        if best_node and best_node.metric and not best_node.metric.is_worst:
            features[4] = best_node.metric.value
        
        # Recent performance trend (last 5 nodes)
        recent_nodes = journal.nodes[-5:] if len(journal.nodes) >= 5 else journal.nodes
        recent_scores = [n.metric.value for n in recent_nodes 
                        if n.metric and not n.metric.is_worst]
        if recent_scores:
            features[5] = np.mean(recent_scores)
            features[6] = np.std(recent_scores) if len(recent_scores) > 1 else 0
        
        # Operator usage statistics
        operator_counts = {"draft": 0, "improve": 0, "debug": 0, "analyze": 0}
        for node in journal.nodes:
            if hasattr(node, 'operators_used') and node.operators_used:
                for op in node.operators_used:
                    if op in operator_counts:
                        operator_counts[op] += 1
        
        features[7:11] = [operator_counts[op] for op in ["draft", "improve", "debug", "analyze"]]
        
        # Success rates by operator
        for i, op in enumerate(["draft", "improve", "debug", "analyze"]):
            op_nodes = [n for n in journal.nodes 
                       if hasattr(n, 'operators_used') and op in n.operators_used]
            if op_nodes:
                success_rate = len([n for n in op_nodes if not n.is_buggy]) / len(op_nodes)
                features[11 + i] = success_rate
        
        # Time-based features
        if journal.nodes:
            total_time = sum(n.exec_time for n in journal.nodes if n.exec_time)
            features[15] = total_time
            features[16] = total_time / len(journal.nodes) if journal.nodes else 0
        
        # Task-specific features (if available)
        if task_info:
            features[17] = task_info.get('complexity', 0)
            features[18] = task_info.get('data_size', 0)
        
        # Normalize features
        features = np.clip(features, -10, 10)  # Prevent extreme values
        return features
    
    def select_action(self, state: np.ndarray, available_actions: List[str]) -> str:
        """Select action using epsilon-greedy policy."""
        # Map available actions to indices
        available_indices = [self.operator_to_action[op] for op in available_actions 
                           if op in self.operator_to_action]
        
        if not available_indices:
            # Fallback to draft if no valid actions
            return "draft"
        
        if random.random() < self.epsilon:
            # Random exploration
            action_idx = random.choice(available_indices)
        else:
            # Greedy exploitation
            with torch.no_grad():
                state_tensor = torch.FloatTensor(state).unsqueeze(0)
                q_values = self.q_network(state_tensor)
                
                # Mask unavailable actions
                masked_q_values = q_values.clone()
                for i in range(self.config.action_dim):
                    if i not in available_indices:
                        masked_q_values[0, i] = float('-inf')
                
                action_idx = masked_q_values.argmax().item()
        
        return self.action_to_operator[action_idx]
    
    def update_policy(self, state: np.ndarray, action: str, reward: float, 
                     next_state: np.ndarray, done: bool):
        """Update the RL policy with new experience."""
        if self.last_state is not None and self.last_action is not None:
            # Store experience in replay buffer
            action_idx = self.operator_to_action[self.last_action]
            self.replay_buffer.push(
                self.last_state, action_idx, reward, state, done
            )
        
        # Update current state and action
        self.last_state = state.copy()
        self.last_action = action
        
        # Train if enough experiences
        if len(self.replay_buffer) >= self.config.batch_size:
            self._train_step()
        
        # Update target network periodically
        if self.step_count % self.config.target_update_freq == 0:
            self.update_target_network()
        
        # Decay epsilon
        self.epsilon = max(self.epsilon * self.config.epsilon_decay, self.config.epsilon_min)
        self.step_count += 1
    
    def _train_step(self):
        """Perform one training step."""
        # Sample batch from replay buffer
        states, actions, rewards, next_states, dones = self.replay_buffer.sample(
            self.config.batch_size
        )
        
        # Current Q values
        current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1))
        
        # Next Q values
        with torch.no_grad():
            if self.config.use_double_dqn:
                # Double DQN: use main network to select actions, target network to evaluate
                next_actions = self.q_network(next_states).argmax(1, keepdim=True)
                next_q_values = self.target_network(next_states).gather(1, next_actions)
            else:
                # Standard DQN
                next_q_values = self.target_network(next_states).max(1)[0].unsqueeze(1)
            
            target_q_values = rewards.unsqueeze(1) + (1 - dones.unsqueeze(1).float()) * 0.99 * next_q_values
        
        # Compute loss and update
        loss = nn.MSELoss()(current_q_values, target_q_values)
        
        self.optimizer.zero_grad()
        loss.backward()
        torch.nn.utils.clip_grad_norm_(self.q_network.parameters(), 1.0)
        self.optimizer.step()
        
        # Log training metrics
        self.logger.debug(f"RL Training - Loss: {loss.item():.4f}, Epsilon: {self.epsilon:.4f}")
    
    def update_target_network(self):
        """Update target network with current network weights."""
        self.target_network.load_state_dict(self.q_network.state_dict())
    
    def save_checkpoint(self, path: str):
        """Save model checkpoint."""
        checkpoint = {
            'q_network_state_dict': self.q_network.state_dict(),
            'target_network_state_dict': self.target_network.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'epsilon': self.epsilon,
            'step_count': self.step_count,
            'episode_count': self.episode_count,
            'config': self.config.__dict__
        }
        torch.save(checkpoint, path)
        self.logger.info(f"RL policy checkpoint saved to {path}")
    
    def load_checkpoint(self, path: str):
        """Load model checkpoint."""
        checkpoint = torch.load(path, map_location='cpu')
        self.q_network.load_state_dict(checkpoint['q_network_state_dict'])
        self.target_network.load_state_dict(checkpoint['target_network_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.epsilon = checkpoint['epsilon']
        self.step_count = checkpoint['step_count']
        self.episode_count = checkpoint['episode_count']
        self.logger.info(f"RL policy checkpoint loaded from {path}")
