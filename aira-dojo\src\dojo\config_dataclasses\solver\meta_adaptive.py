# Copyright (c) Meta Platforms, Inc. and affiliates.
# All rights reserved.
#
# This source code is licensed under the license found in the
# LICENSE file in the root directory of this source tree.

"""
Configuration dataclass for Meta-Adaptive Solver.
"""

from dataclasses import dataclass, field
from omegaconf import MISSING

from dojo.config_dataclasses.solver.base import SolverConfig


@dataclass
class MetaAdaptiveSolverConfig(SolverConfig):
    """Configuration for Meta-Adaptive Solver with meta-learning capabilities."""
    
    # Basic solver parameters
    step_limit: int = field(
        default=500,
        metadata={"help": "Maximum number of search steps"}
    )
    
    # Meta-learning components configuration
    use_rl_policy: bool = field(
        default=True,
        metadata={"help": "Whether to use RL-based operator selection"}
    )
    
    use_bayesian_optimization: bool = field(
        default=True,
        metadata={"help": "Whether to use Bayesian optimization for hyperparameters"}
    )
    
    use_adaptive_scheduling: bool = field(
        default=True,
        metadata={"help": "Whether to use adaptive temperature scheduling"}
    )
    
    use_meta_learning: bool = field(
        default=True,
        metadata={"help": "Whether to use meta-learning from previous tasks"}
    )
    
    use_nas: bool = field(
        default=False,
        metadata={"help": "Whether to use Neural Architecture Search"}
    )
    
    # RL Policy configuration
    rl_hidden_dim: int = field(
        default=128,
        metadata={"help": "Hidden dimension for RL policy network"}
    )
    
    rl_learning_rate: float = field(
        default=1e-3,
        metadata={"help": "Learning rate for RL policy"}
    )
    
    rl_epsilon: float = field(
        default=0.1,
        metadata={"help": "Epsilon for epsilon-greedy exploration"}
    )
    
    rl_checkpoint_path: str = field(
        default="",
        metadata={"help": "Path to save/load RL policy checkpoint"}
    )
    
    # Bayesian Optimization configuration
    bo_initial_points: int = field(
        default=5,
        metadata={"help": "Number of initial random points for BO"}
    )
    
    bo_max_iterations: int = field(
        default=50,
        metadata={"help": "Maximum iterations for BO"}
    )
    
    bo_acquisition: str = field(
        default="ei",
        metadata={"help": "Acquisition function for BO (ei, pi, ucb)"}
    )
    
    bo_checkpoint_path: str = field(
        default="",
        metadata={"help": "Path to save/load BO checkpoint"}
    )
    
    # Adaptive Scheduling configuration
    initial_temp: float = field(
        default=1.0,
        metadata={"help": "Initial temperature for adaptive scheduling"}
    )
    
    final_temp: float = field(
        default=0.1,
        metadata={"help": "Final temperature for adaptive scheduling"}
    )
    
    adaptation_rate: float = field(
        default=0.1,
        metadata={"help": "Rate of adaptation for temperature scheduling"}
    )
    
    scheduler_strategy: str = field(
        default="performance",
        metadata={"help": "Strategy for adaptive scheduling (performance, diversity, etc.)"}
    )
    
    # Meta-Learning configuration
    meta_hidden_dim: int = field(
        default=128,
        metadata={"help": "Hidden dimension for meta-learning networks"}
    )
    
    meta_learning_rate: float = field(
        default=1e-3,
        metadata={"help": "Learning rate for meta-learning"}
    )
    
    knowledge_base_path: str = field(
        default="",
        metadata={"help": "Path to save/load knowledge base"}
    )
    
    # NAS configuration
    nas_strategy: str = field(
        default="evolutionary",
        metadata={"help": "Strategy for NAS (random, evolutionary, differentiable)"}
    )
    
    nas_population_size: int = field(
        default=10,
        metadata={"help": "Population size for evolutionary NAS"}
    )
    
    nas_generations: int = field(
        default=20,
        metadata={"help": "Number of generations for evolutionary NAS"}
    )
    
    # Search behavior configuration
    max_debug_depth: int = field(
        default=5,
        metadata={"help": "Maximum depth for debugging cycles"}
    )
    
    convergence_threshold: float = field(
        default=0.001,
        metadata={"help": "Threshold for convergence detection"}
    )
    
    use_complexity: bool = field(
        default=False,
        metadata={"help": "Whether to use complexity-aware prompting"}
    )
    
    lower_is_better: bool = field(
        default=False,
        metadata={"help": "Whether lower metric values are better"}
    )
    
    # Logging and export configuration
    export_search_results: bool = field(
        default=True,
        metadata={"help": "Whether to export search results"}
    )
    
    detailed_logging: bool = field(
        default=True,
        metadata={"help": "Whether to enable detailed logging"}
    )
    
    # Advanced features
    enable_operator_adaptation: bool = field(
        default=True,
        metadata={"help": "Whether to adapt operator selection based on performance"}
    )
    
    enable_dynamic_hyperparams: bool = field(
        default=True,
        metadata={"help": "Whether to dynamically adjust hyperparameters"}
    )
    
    enable_cross_task_learning: bool = field(
        default=True,
        metadata={"help": "Whether to learn from previous tasks"}
    )
    
    # Performance monitoring
    performance_window_size: int = field(
        default=10,
        metadata={"help": "Window size for performance monitoring"}
    )
    
    early_stopping_patience: int = field(
        default=20,
        metadata={"help": "Patience for early stopping"}
    )
    
    min_improvement_threshold: float = field(
        default=0.01,
        metadata={"help": "Minimum improvement threshold for early stopping"}
    )
